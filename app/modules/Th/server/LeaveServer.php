<?php
/**
 * Created by <PERSON>p<PERSON>tor<PERSON>.
 * User: nick
 * Date: 2021/9/8
 * Time: 2:26 PM
 */

namespace FlashExpress\bi\App\Modules\Th\Server;

use Exception;
use FlashExpress\bi\App\Enums\LeaveEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\StaffAuditImageModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffDaysFreezeModel;
use FlashExpress\bi\App\Models\backyard\StaffLastYearDaysModel;
use FlashExpress\bi\App\Models\backyard\StaffLeaveRemainDaysModel;
use FlashExpress\bi\App\Modules\Th\Server\Vacation\SickServer;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\LeaveServer AS GlobalBaseServer;
use FlashExpress\bi\App\Modules\Th\Server\Vacation\AnnualServer;
use FlashExpress\bi\App\Server\SyncServer;
use FlashExpress\bi\App\Modules\Th\Server\Vacation\InternationalServer;
use FlashExpress\bi\App\Modules\Th\Server\Vacation\MaternityServer;


class LeaveServer extends GlobalBaseServer {

    public $leaveObject;
    //1月1号 需要初始化数据的类型
    public $initType = [enums::LEAVE_TYPE_38,enums::LEAVE_TYPE_19,enums::LEAVE_TYPE_4];


    //返回 带薪事假 额度 如果 预申请明年额度 会初始化一条记录 变态玩意
    public function check_personal_leave($staff_info, $year)
    {
        //请去年的 额度失效了 不让请
        if ($year < date('Y')) {
            return 0;
        }

        $remain_info = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
            'bind'       => [
                'staff_id'   => $staff_info['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_2,
                'year_at'    => $year,
            ],

        ]);

        //如果 没有额度 数据 需要初始化一条
        if (empty($remain_info)) {
            $remain_info = new StaffLeaveRemainDaysModel();
            //获取 对应年 已经申请的假期
            $audit_re = new AuditRepository($this->lang);
            $used_day = 0;
            $used     = $audit_re->get_used_leave_days($staff_info['staff_info_id'], $year, '2');
            if (!empty($used) && !empty($used[0]['num'])) {
                $used_day = $used[0]['num'];
            }

            $row['staff_info_id'] = $staff_info['staff_info_id'];
            $row['leave_type']    = enums::LEAVE_TYPE_2;
            $row['year']          = $year;
            $row['days']          = max(enums::PERSONAL_DAYS_UN - $used_day,0);//剩余
            $row['leave_days']    = $used_day;//已使用
            $remain_info->create($row);
        }
        return max(enums::PERSONAL_DAYS_UN - max($remain_info->leave_days,0),0);
    }


    //获取带薪事假 额度 这个只有当前年的额度 如果申请跨年 明年的 走下面的 check_personal_leave 方法
    public function get_personal_days($staff_info): array
    {
        $freeze_info = StaffLeaveRemainDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and leave_type = :leave_type: and year = :year_at:',
            'bind'       => [
                'staff_id'   => $staff_info['staff_info_id'],
                'leave_type' => enums::LEAVE_TYPE_2,
                'year_at'    => date('Y'),
            ],

        ]);
        //基础额度 就是 3
        $da['day_limit'] = enums::PERSONAL_DAYS_UN;//总额
        if (empty($freeze_info)) {
            $da['day_sub'] = enums::PERSONAL_DAYS_UN;//剩余
            $da['day_sum'] = 0;                      //已使用
            return $da;
        }
        $da['day_sub'] = max($da['day_limit'] - max($freeze_info->leave_days,0), 0);//剩余
        $da['day_sum'] = $freeze_info->leave_days;                           //已使用
        return $da;
    }




    /** 申请假期 保存
     * @param $param
     * @throws ValidationException
     * @return array
     */
    public function saveVacation($param){
        $this->leaveObject = $this->getLeaveObj(intval($param['leave_type']));
        $auditServer = new AuditServer($this->lang, $this->timeZone);
        $staffRe    = new StaffRepository($this->lang);
        $staffInfo  = $staffRe->getStaffPosition($param['staff_id']);
        $leave_lang = AuditRepository::$leave_type;
        $typeData   = $auditServer->staffLeaveType($staffInfo);
        if (empty($typeData)) {
            throw new ValidationException('wrong leave type');
        }
        if (!in_array($param['leave_type'], array_keys($typeData)) && $param['leave_type'] != enums::LEAVE_TYPE_15) {
            throw new ValidationException($this->getTranslation()->_('jobtransfer_0004').$this->getTranslation()->_($leave_lang[$param['leave_type']]));
        }

        $db = $this->getDI()->get('db');
        $db->begin();
        try{
            $audit_id = $this->leaveObject->handleCreate($param);
            //没生成id
            if (empty($audit_id)) {
                throw new ValidationException($this->getTranslation()->_('1009'));
            }

            //二次确认结构 code ->1 msg -> xxx data -> ['is_error' -> 1]
            if(is_array($audit_id)){
                return $audit_id;
            }

            //非工具操作申请 创建审批相关
            if (empty($param['is_bi'])) {
                $param['time_out'] = $this->leaveObject->timeOut ?? null;
                $auditServer->saveApproval($audit_id, $param);
            }

            $db->commit();
            $return['data'] = ['leave_day' => $this->leaveObject->leave_day ?? 0];

        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }

        if(!empty($param['is_bi']) && $param['is_bi'] == 1) {
            //https://flashexpress.feishu.cn/docx/Tcw8dzXraoWFqhxJvEIcIxqznxb
            //请假审批通过同步bi，重算处罚数据
            $sync_server = new SyncServer($this->lang, $this->timezone);
            $params      = [
                'staff_id'         => $param['staff_id'],
                'leave_start_date' => $param['leave_start_time'],
                'leave_end_date'   => $param['leave_end_time'],
            ];
            $sync_server->sync_fbi_attendance($params, 'abnormal.staff_leave_request');
        }

        return $this->checkReturn($return);
    }

    //非审核通过 撤销操作 返还额度
    public function cancelVacation($auditInfo,$staffInfo,$state, $extend = []){
        $extend['status'] = $state;
        $this->leaveObject = $this->getLeaveObj(intval($auditInfo['leave_type']));
        $this->leaveObject->returnRemainDays($auditInfo['audit_id'],$staffInfo,$extend);
    }

    //获取对应假期的额度
    public function getVacationDays($staffId,$leaveType, $extend = []){
        $param['staff_id'] = $staffId;
        $param['leave_type'] = $leaveType;
        $param = array_merge($param,$extend);
        $leaveObject = $this->getLeaveObj($param['leave_type']);
        return $leaveObject->handleSearch($param);
    }



    /**
     * 对应本国的 所有类型 映射类
     * @param $leaveType
     * @throws ValidationException
     */
    public function getLeaveObj(int $leaveType){
        //对应假期类型 实例
        $leaveObj = \stdClass::class;
        switch ($leaveType){
            case enums::LEAVE_TYPE_1://年假
                $leaveObj =  new AnnualServer($this->lang,$this->timeZone);
                break;
            case enums::LEAVE_TYPE_2:
                break;
            case enums::LEAVE_TYPE_38://病假
                $leaveObj =  new SickServer($this->lang,$this->timeZone);
                break;
            case enums::LEAVE_TYPE_19://跨国探亲
                $leaveObj =  new InternationalServer($this->lang,$this->timeZone);
                break;
            case enums::LEAVE_TYPE_4:
                $leaveObj =  new MaternityServer($this->lang,$this->timeZone);
                break;
            default:
                throw new ValidationException('WRONG TYPE');
        }
        return $leaveObj;
    }

    public function getInstanceObj(int $leaveType){
        //对应假期类型 实例
        switch ($leaveType){
            case enums::LEAVE_TYPE_19://跨国探亲
                $leaveObj = InternationalServer::getInstance($this->lang,$this->timezone);
                break;
            case enums::LEAVE_TYPE_38://病假
                $leaveObj = SickServer::getInstance($this->lang,$this->timezone);
                break;
            case enums::LEAVE_TYPE_4://产假
                $leaveObj = MaternityServer::getInstance($this->lang,$this->timezone);
                break;
            default:
                throw new ValidationException('WRONG TYPE');
        }
        return $leaveObj;
    }

    //获取 额度详情信息列表
    public function getAnnualDetail($param){
        $leaveObject = $this->getLeaveObj(enums::LEAVE_TYPE_1);
        return $leaveObject->detailList($param);
    }

    /**
     * @description 带薪事假请假原因
     * @return array[]
     */
    public function getPaidLeaveReasonList(): array
    {
        $t = $this->getTranslation();
        return [
            [
                'label' => $t->_('paid_leave_reason_1'),
                'value' => LeaveEnums::PAID_LEAVE_REASON_GOV
            ],
            [
                'label' => $t->_('paid_leave_reason_2'),
                'value' => LeaveEnums::PAID_LEAVE_REASON_GRADUATION
            ],
            [
                'label' => $t->_('paid_leave_reason_3'),
                'value' => LeaveEnums::PAID_LEAVE_REASON_ATTEND_CEREMONY
            ],
            [
                'label' => $t->_('paid_leave_reason_4'),
                'value' => LeaveEnums::PAID_LEAVE_REASON_CARE
            ],
            [
                'label' => $t->_('paid_leave_reason_5'),
                'value' => LeaveEnums::PAID_LEAVE_REASON_ACCIDENT
            ],
            [
                'label' => $t->_('paid_leave_reason_6'),
                'value' => LeaveEnums::PAID_LEAVE_REASON_BIRTHDAY
            ],
        ];
    }

    /**
     * @description 获取带薪事假请假原因
     * @param $code
     * @return string
     */
    public function getPaidLeaveReason($code): string
    {
        $list = $this->getPaidLeaveReasonList();
        $list = array_column($list, 'label', 'value');
        return $list[$code] ?? "";
    }


}