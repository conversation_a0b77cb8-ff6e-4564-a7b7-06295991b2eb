<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\Models\backyard\HrJdModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\ResumeRecommendRepository;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SysStoreServer;

class ResumeRecommendServer extends \FlashExpress\bi\App\Server\ResumeRecommendServer{

    /**
     * 获取推荐简历下拉初始化
     * return array
     */
    public function getResumeRecommendInit($paramsIn){
        $returnArr = [
            'code'=>1,
            'data'=>'',
            'msg'=>''
        ];
        $settingServer = new SettingEnvServer();
        $recommendKdJd = $settingServer->getSetVal('by_recommender_resume_jd_kd');

        $recommendOtherJd = $settingServer->getSetVal('by_recommender_resume_jd_other');

        $recommendKdJd = !empty( $recommendKdJd ) ? explode(',',$recommendKdJd) : [] ;
        $recommendOtherJd = !empty( $recommendOtherJd ) ? explode(',',$recommendOtherJd) : [] ;
        $jdIds = array_merge($recommendOtherJd,$recommendKdJd);

        $jds = $settingServer->getSetVal('by_recommender_resume_jd');
        $jds = explode(',', $jds);

        //查询hr_hc表 组装下拉选项数据
        $builderHrHc = $this->modelsManager->createBuilder();
        $builderHrHc->columns([
            'jd.job_name',
            'jd.job_id',
        ])->from(['jd' => HrJdModel::class]);
        $builderHrHc->inWhere('jd.job_id',$jdIds);
        $hrJdRst = $builderHrHc->getQuery()->execute()->toArray();

        foreach ( $hrJdRst as $k=>$val ){

            if( in_array( $val['job_id'],$recommendKdJd) && (in_array('ALL', $jds) || in_array($val['job_id'], $jds))) {
                $hrJdRst[$k]['job_type'] = ResumeRecommendRepository::EXPROESS_JOB_YES;
            }
            if( in_array( $val['job_id'],$recommendOtherJd ) ){
                $hrJdRst[$k]['job_type'] = ResumeRecommendRepository::EXPROESS_JOB_OTHER;
            }

        }
        $data['expect_job'] = $hrJdRst;
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind' => [ 'staff_info_id' => $paramsIn['userinfo']['staff_id'] ],
            'columns' => 'sys_store_id'
        ]);
        //初始化 网点数据
        $data['estimate_store'] = [
            'organization_id'=>'',
            'organization_name'=> ''
        ];
        if( !empty( $staffInfo ) ){
            $staffInfo = $staffInfo->toArray();
            if( $staffInfo['sys_store_id'] == -1 ){

                $data['estimate_store'] ['organization_id'] = -1;
                $data['estimate_store'] ['organization_name'] = 'Head Office';

            }else{

                $storeInfo = (new SysStoreServer())->getStoreByid($staffInfo['sys_store_id']);
                if( !empty( $storeInfo ) ){
                    $data['estimate_store'] ['organization_id'] = $storeInfo['id'];
                    $data['estimate_store'] ['organization_name'] = $storeInfo['name'];
                }

            }
        }

        $recommend = new ResumeRecommendRepository();
        //简历驳回数量
        $data['resumeRecommendRejectNum'] = $recommend->getResumeRejectNum($paramsIn['userinfo']);

        //简历待提交数量
        $data['resumeRecommendSubmittedNum'] = $recommend->getResumeSumittedNum($paramsIn['userinfo']);

        $returnArr['data'] = $data;
        return $this->checkReturn($returnArr);
    }

    public function jdList($reserve_type = 0)
    {
        $settingServer = new SettingEnvServer();
        $jds = $settingServer->getSetVal('by_recommender_resume_jd');
        $jds = explode(',', $jds);
        $jdOther = $settingServer->getSetVal('by_recommender_resume_jd_other');
        $jdOther = explode(',', $jdOther);

        $recommendKdJd = $settingServer->getSetVal('by_recommender_resume_jd_kd');
        $recommendKdJd = !empty( $recommendKdJd ) ? explode(',',$recommendKdJd) : [] ;

        if ($reserve_type == HrResumeModel::RESERVE_TYPE_AGENT){
            $jdAgent = $settingServer->getSetVal('individual_contractor_jobids');
            $jdList = HrJdModel::find([
                'conditions' => ' state = 1 and job_id in ('.$jdAgent.')'
            ])->toArray();
        }else{
            $jdList = HrJdModel::find([
                'conditions' => ' state = 1'
            ])->toArray();
        }
        $result = ['hot_list' => [], 'list' => []];
        $list = [];
        foreach ($jdList as $jd) {
            if ($reserve_type != HrResumeModel::RESERVE_TYPE_AGENT) {
                // 个人代理不显示hot
                if (in_array($jd['job_id'], $jdOther) && (in_array('ALL', $jds) || in_array($jd['job_id'], $jds))) {
                    $result['hot_list'][] = [
                        'job_id'   => $jd['job_id'],
                        'job_name' => $jd['job_name'],
                        'job_type' => in_array($jd['job_id'], $recommendKdJd) ? 1 : 2,
                    ];
                }
            }

            if ((in_array('ALL', $jds) || in_array($jd['job_id'], $jds))) {
                $list[substr(strtolower($jd['job_name']), 0, 1)][] = [
                    'job_id' => $jd['job_id'],
                    'job_name' => $jd['job_name'],
                    'job_type' => in_array($jd['job_id'], $recommendKdJd) ? 1 : 2,
                ];
            }
        }

        $range =
            range('A', 'Z');
        foreach ($range as $item) {
            $result['list'][] = [
                'index' => $item,
                'list' => isset($list[strtolower($item)]) &&  $list[strtolower($item)] ? $list[strtolower($item)] : []
            ];
        }

        return $result;
    }

    public function SearchList($name,$reserve_type = 0)
    {
        $settingServer = new SettingEnvServer();
        $jds = $settingServer->getSetVal('by_recommender_resume_jd');
        $jds = explode(',', $jds);

        $recommendKdJd = $settingServer->getSetVal('by_recommender_resume_jd_kd');
        $recommendKdJd = !empty( $recommendKdJd ) ? explode(',',$recommendKdJd) : [] ;

        if ($reserve_type == HrResumeModel::RESERVE_TYPE_AGENT){
            $jdAgent = $settingServer->getSetVal('individual_contractor_jobids');
            $jdList = HrJdModel::find([
                'conditions' => ' state = 1 and job_id in ('.$jdAgent.')'
            ])->toArray();
        }else{
            $jdList = HrJdModel::find([
                'conditions' => ' state = 1'
            ])->toArray();
        }

        $list = [];
        foreach ($jdList as $jd) {
            if (in_array('ALL', $jds) || in_array($jd['job_id'], $jds)) {
                if (strpos(strtolower($jd['job_name']), strtolower($name)) !== false) {
                    $list[] = [
                        'job_id' => $jd['job_id'],
                        'job_name' => $jd['job_name'],
                        'job_type' => in_array($jd['job_id'], $recommendKdJd) ? 1 : 2,
                    ];
                }
            }
        }

        return $list;
    }

    public function getStoreHc($params,$userInfo){
        //store_id : 我的网点
        $params['store_id'] = $userInfo['organization_type'] == 1 ? $userInfo['organization_id'] : '';
        if (empty($params['store_tab']) || $params['store_tab'] == 1) {
            //当前登录主管所在hc job
            $data               = (new ResumeRecommendRepository($this->lang))->getHcByStore($params, $userInfo);
        } else {
            //其他所有网点hc
            $data = (new ResumeRecommendRepository($this->lang))->getHcListV1($params);
        }
        return $data;

    }
}

