<?php


namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStoreApplySupportModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Modules\Th\library\Enums\HrJobTitleEnums;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\OsStaffRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\StoreSupportRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
//use FlashExpress\bi\App\Server\BaseServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\AuditOptionRule;
use FlashExpress\bi\App\Server\ResumeServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffSupportStoreServer as BaseStaffSupportStoreServer;
use Exception;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysStoreServer;

class StaffSupportStoreServer extends BaseStaffSupportStoreServer
{
    public $timezone;
    public $lang;
    //public $country_server;

    protected $store_support;
    protected $os;
    protected $staff;
    protected $auditlist;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        $this->timezone = $timezone;
        $this->lang     = $lang;
        $this->os = new OsStaffRepository($timezone);
        $this->store_support = new StoreSupportRepository($timezone);
        $this->staff = new StaffRepository();
        $this->auditlist= new AuditlistRepository($this->lang, $this->timezone);

        parent::__construct($lang);
    }

    //员工申请支援网点
    public function addStaffApplySupport($paramIn) {

        $serial_no  = $this->processingDefault($paramIn, 'id');
        $staff_info_id = $this->processingDefault($paramIn, 'staff_id');
        $validate_van_courier = $paramIn['validate_van_courier'] ?? 0;

        $store_apply_detail = HrStoreApplySupportModel::findFirst([
            'conditions' => "serial_no = :serial_no: AND support_status IN ({support_status:array})",
            'bind' => [
                'serial_no' => $serial_no,
                'support_status' => [HrStoreApplySupportModel::SUPPORT_STATUS_RECRUIT]
            ],
        ]);

        if(empty($store_apply_detail)) {
            throw new BusinessException($this->getTranslation()->_('data_error'));//未找到数据
        }
        $store_apply_detail = $store_apply_detail->toArray();

        if($store_apply_detail['status'] != 2) {
            throw new BusinessException($this->getTranslation()->_('data_error'));//网点申请支援数据状态不正确
        }
        //新增 验证 支援判断是否存在出差  https://flashexpress.feishu.cn/wiki/R7VBwtiL1ilPSGkQgXQczZcAnbg
        $tripServer = new BusinesstripServer($this->lang, $this->timezone);
        $existTrip = $tripServer->checkExistTime($store_apply_detail['employment_begin_date'],$store_apply_detail['employment_end_date'], $staff_info_id);
        if (!empty($existTrip)) {
//            [$noticeStart, $noticeEnd] = [$existTrip[0]['start_time'], $existTrip[0]['end_time']];
            throw new ValidationException($this->getTranslation()->_('support_check_trip'));
        }

        $staff_info = $this->staff->checkoutStaff($staff_info_id);

        if($staff_info['organization_id'] == $store_apply_detail['store_id']) {
            throw new BusinessException($this->getTranslation()->_('store_support_error_7'));//不能申请支援自己所属网点的支援
        }
        //todo 申请支援改动点(3-2)
        //branch_supervisor、Van Courier、Bike Courier、dc_officer assistant_branch_supervisor申请
        //Senior Branch Supervisor
        if(!in_array($staff_info['job_title'], [
            enums::$job_title['van_courier'],
            enums::$job_title['bike_courier'],
            enums::$job_title['dc_officer'],
            enums::$job_title['cdc_officer'],
            enums::$job_title['branch_supervisor'],
            enums::$job_title['assistant_branch_supervisor'],
            enums::$job_title['cdc_supervisor'],
            HrJobTitleEnums::JOB_TITLE_SENIOR_BRANCH_SUPERVISOR,
        ])){
            throw new BusinessException($this->getTranslation()->_('store_support_error_1'));
        }

        if($staff_info['job_title'] == enums::$job_title['dc_officer'] &&
            !in_array($store_apply_detail['job_title_id'], [
                enums::$job_title['van_courier'],
                enums::$job_title['bike_courier'],
                enums::$job_title['dc_officer']
            ])) {
            throw new BusinessException($this->getTranslation()->_('store_support_error_6'));//dc_officer 不能申请branch_supervisor
        }

        if(in_array($staff_info['job_title'], [enums::$job_title['van_courier'], enums::$job_title['bike_courier']]) && $staff_info['job_title'] != $store_apply_detail['job_title_id']){
            throw new BusinessException($this->getTranslation()->_('store_support_error_8'));//只能申请和自己职位相同的网点支援
        }

        if($staff_info['job_title'] == enums::$job_title['cdc_officer'] && !in_array($store_apply_detail['job_title_id'],[enums::$job_title['van_courier'], enums::$job_title['bike_courier'], enums::$job_title['dc_officer']])){
            throw new BusinessException($this->getTranslation()->_('store_support_error_13'));//只能申请Bike Courier、Van Courier、DC Officer职位
        }
        if(in_array($staff_info['job_title'], [enums::$job_title['cdc_supervisor'], HrJobTitleEnums::JOB_TITLE_SENIOR_BRANCH_SUPERVISOR]) &&
            !in_array($store_apply_detail['job_title_id'],[enums::$job_title['van_courier'], enums::$job_title['bike_courier'], enums::$job_title['dc_officer'],enums::$job_title['branch_supervisor']])){
            throw new BusinessException($this->getTranslation()->_('store_support_error_14'));//只能申请Bike Courier、Van Courier、DC Officer 、Branch Supervisor职位
        }

        $is_applying = $this->isApplySerialNo($serial_no, $staff_info_id);
        if($is_applying) {
            throw new BusinessException($this->getTranslation()->_('store_support_error_2'));//已经申请过该工单不能重复申请
        }

        //判断员工是否跟其他申请的支援任务有冲突
        $is_conflict = $this->validateConflict($store_apply_detail['employment_begin_date'], $store_apply_detail['employment_end_date'], $staff_info_id);
        if($is_conflict) {
            throw new BusinessException($this->getTranslation()->_('store_support_error_3'));//工作日期冲突，不能申请
        }

        //判断该网点申请支援单号是需求人数是否被招满
        if($this->isStaffSupportFull($serial_no ,$store_apply_detail['final_audit_num'])) {
            throw new BusinessException($this->getTranslation()->_('store_support_error_4'));//该订单已经被招满
        }

        if(empty($validate_van_courier) && (in_array($staff_info['job_title'],[enums::$job_title['branch_supervisor'],enums::$job_title['cdc_officer'],enums::$job_title['cdc_supervisor']]) && $store_apply_detail['job_title_id'] == enums::$job_title['van_courier'])) {
            //您正在申请职位为Van Courier的跨网点支援，请确保支援期间您有可使用的车辆
            return $this->checkReturn(['code' => 1, 'msg' => $this->getTranslation()->_('store_support_error_9'), 'data' => ['is_error' => 1]]);
        }

        // 计算两个网点经纬度之间的距离
        $apart = (new SysStoreServer())->calculateDistanceStore($store_apply_detail['store_id'],$staff_info['organization_id']);

        $db = $this->getDI()->get('db');
        try {
            $insertData = [
                'serial_no'             => 'SASS' . $this->getRandomId(), //审批编号
                'staff_info_id'         => $staff_info_id, //申请人工号
                'store_apply_support_serial_no' => $serial_no, //关联网点申请支援编号
                'job_title_id'          => $store_apply_detail['job_title_id'], //申请职位
                'store_id'              => $store_apply_detail['store_id'],
                'store_name'            => $store_apply_detail['store_name'],
                'employment_begin_date' => $store_apply_detail['employment_begin_date'],
                'employment_end_date'   => $store_apply_detail['employment_end_date'],
                'employment_days'       => $store_apply_detail['employment_days'],
                'shift_id'              => $store_apply_detail['shift_id'],
                'shift_type'            => $store_apply_detail['shift_type'],
                'shift_start'           => $store_apply_detail['shift_start'],
                'shift_end'             => $store_apply_detail['shift_end'],
                'status'                => enums::$audit_status['panding'],
                'staff_store_id'        => $staff_info['organization_id'], //申请人所属网点取的是ms库staff_info表organization_id
                'apart'                 => $apart, //两个网点经纬度之间的距离
            ];

            $db->begin();
            $apply_id = $this->store_support->insert_store_support('hr_staff_apply_support_store' ,$insertData);
            if (empty($apply_id)) {
                throw new Exception($this->getTranslation()->_('4008'));
            }
            $apply_staff_store = (new SysStoreServer())->getStoreRegionPiece($paramIn['param']['store_id']);

            $is_same_piece = 0;
            if($apply_staff_store['manage_piece'] == $store_apply_detail['store_piece_id']) {
                $is_same_piece = 1;
            }
            $extend = [
                'job_title' => $store_apply_detail['job_title_id'],
                'is_same_piece' => $is_same_piece, //申请人网点所在片区是否和支援网点在同一片区
            ];

            //创建审批流
            $server = new ApprovalServer($this->lang, $this->timezone);
            $requestId = $server->create($apply_id, AuditListEnums::APPROVAL_TYPE_SASS, $staff_info_id, null, $extend);
            if (!$requestId) {
                throw new Exception('创建审批流失败');
            }
            $db->commit();
            return $this->checkReturn([]);
        } catch (Exception $e) {
            $db->rollback();
            $this->getDI()->get('logger')->write_log("addStaffApplySupport failure:" . $e->getMessage() . $e->getTraceAsString());
            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    //更新审批
    public function updateStaffApplySupport($paramIn) {
        $staff_info_id = $this->processingDefault($paramIn, 'staff_id', 2);
        $status  = $this->processingDefault($paramIn, 'status', 2);
        $reason  = $this->processingDefault($paramIn, 'reject_reason', 1);
        $apply_id = $this->processingDefault($paramIn, 'audit_id', 2);
        $reason   = addcslashes(stripslashes($reason),"'");

        //详情
        $detail = $this->getStaffApplySupportDetail($apply_id);
        if (empty($detail)) {
            throw new Exception($this->getTranslation()->_('4008'), enums::$ERROR_CODE['1000']);
        }

        if ($detail['status'] != enums::$audit_status['panding'] && $status == enums::$audit_status['revoked']) {
            throw new Exception($this->getTranslation()->_('please try again'), enums::$ERROR_CODE['1000']);
        }

        try {
            //同意或者驳回等分开处理
            if ($status == enums::$audit_status['approved']) {
                $store_apply_detail = HrStoreApplySupportModel::findFirst([
                    'conditions' => "serial_no = :serial_no:",
                    'bind' => [
                        'serial_no' => $detail['store_apply_support_serial_no']
                    ],
                ]);
                if($this->isStaffSupportFull($detail['store_apply_support_serial_no'], $store_apply_detail->final_audit_num)) {
                    throw new Exception($this->getTranslation()->_('store_support_error_4'), enums::$ERROR_CODE['1000']);
                }

                //判断支援网点是否和申请人当前网点是否一直，如果一致提示只能驳回
                $staff_info = $this->staff->checkoutStaff($detail['staff_info_id']);
                if($detail['store_id'] == $staff_info['organization_id']) {
                    throw new Exception($this->getTranslation()->_('store_support_error_10'), enums::$ERROR_CODE['1000']);
                }

                //同意
                $server = new ApprovalServer($this->lang, $this->timezone);
                $server->approval($apply_id, AuditListEnums::APPROVAL_TYPE_SASS, $staff_info_id);
            } else if ($status == enums::$audit_status['dismissed']) {
                //驳回
                $server = new ApprovalServer($this->lang, $this->timezone);
                $server->reject($apply_id, AuditListEnums::APPROVAL_TYPE_SASS, $reason, $staff_info_id);
            } else {
                //撤销
                $server = new ApprovalServer($this->lang, $this->timezone);
                $server->cancel($apply_id, AuditListEnums::APPROVAL_TYPE_SASS, $reason, $staff_info_id);
            }
            return $this->checkReturn([]);
        } catch (Exception $e) {
            if ($e->getCode() == '1000') {
                $this->getDI()->get('logger')->write_log("updateStoreApplySupport failure:" . $e->getMessage() . $e->getTraceAsString(), "info");
            } else {
                $this->getDI()->get('logger')->write_log("updateStoreApplySupport failure:" . $e->getMessage() . $e->getTraceAsString(), "notice");
            }
            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    /*
    //申请详情
    public function getStaffApplySupportDetail($apply_id) {
        $staff_apply_detail = HrStaffApplySupportStoreModel::findFirst([
            'conditions' => "id = :apply_id:",
            'bind' => [
                'apply_id' => $apply_id
            ]
        ]);

        $detail = [];
        if(!empty($staff_apply_detail)) {
            $detail = $staff_apply_detail->toArray();
            $shift_info  = $this->os->getWorkShift($detail['shift_id']);
            if (!empty($shift_info)) {
                if ($shift_info['type'] == 'EARLY') {
                    $shift = $this->getTranslation()->_('shift_early');
                } else if ($shift_info['type'] == 'MIDDLE') {
                    $shift = $this->getTranslation()->_('shift_middle');
                } else if ($shift_info['type'] == 'NIGHT') {
                    $shift = $this->getTranslation()->_('shift_night');
                } else {
                    $shift = $this->getTranslation()->_('shift_early');
                }
                $detail['work_shift'] = $shift . ' ' . $shift_info['start'] . ' - ' . $shift_info['end'];
            }
        }

        return $detail;
    }
    */

    //申请详情
    public function getDetail(int $auditId, $user, $comeFrom) {
        $result = $this->getStaffApplySupportDetail($auditId);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($result['staff_info_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }

        $detailLists = [
            'apply_parson'      => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? ''),
            'apply_department'  => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? ''),
            'store_support_support_apply_type' => $this->getTranslation()->_('store_support_support_apply_type_1'),
            'store_support_staff_job_title' => UC('storeSupport')['jobTitles'][$result['job_title_id']] ?? '',
            'store_name'        => $result['store_name'],
            'store_support_employment_begin_date' => $result['employment_begin_date'],
            'employment_days'   => $result['employment_days'],
            'work_shift'        => $result['work_shift'],
        ];

        //驳回状态，需要显示驳回原因
        if ($result['status'] == enums::$audit_status['dismissed']) {
            $detailLists = array_merge($detailLists, ['reject_reason' => $result['reject_reason'] ?? '']);
        }
        $returnData['data']['detail'] = $this->format($detailLists);
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $data = [
            'title'      => $this->auditlist->getAudityType(enums::$audit_type['SASS']),
            'id'         => $result['id'],
            'staff_id'   => $result['staff_info_id'],
            'type'       => enums::$audit_type['SASS'],
            'created_at' => date('Y-m-d H:i:s', strtotime($result['created_at']) + $add_hour * 3600),
            'updated_at' => date('Y-m-d H:i:s', strtotime($result['updated_at']) + $add_hour * 3600),
            'status'     => $result['status'],
            'status_text'=> $this->auditlist->getAuditStatus('10' . $result['status']),
            'serial_no'  => $result['serial_no'] ?? '',
            'employment_days'   => $result['employment_days'],
        ];

        if ($result['status'] == 3) {
            $returnData['data']['confirm'] = [
                ['key' => $this->getTranslation()->_('reject_reason'), 'value' => $result['reject_reason']],
            ];
        }

        $returnData['data']['head']   = $data;

        return $returnData;
    }

    /**
     * @param $auditId
     * @return AuditOptionRule
     */
    public function getOptionsRule($auditId)
    {
        return new AuditOptionRule(true, false, false, false, false, false);
    }
    /**
     * 设置回调属性
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return mixed|void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            $params['status'] = $state;
            if ($state == enums::APPROVAL_STATUS_APPROVAL) {
                $params['approval_agreed_time'] = gmdate('Y-m-d H:i:s');
            } else {
                $params['support_status'] = 4;
            }
            $this->getDI()->get('db')->updateAsDict(
                'hr_staff_apply_support_store',
                $params,
                'id = ' . $auditId
            );
            if ($state == enums::APPROVAL_STATUS_APPROVAL) {
                $apply_detail = $shiftParam = $this->getStaffApplySupportDetail($auditId);
                $today = date('Y-m-d');
                $isOpen = (new SettingEnvServer())->getSetVal('sub_staff_support_switch');
                $sub_staff_info_id = 0;
                if($isOpen == 1) {
                    if($today >= $apply_detail['employment_begin_date'] && $today <= $apply_detail['employment_end_date']) {
                        //创建子账号，回写到申请表
                        $sub_result = $this->createSubStaff($apply_detail);
                        if ($sub_result['code'] == 1) {
                            //同步ms数据
                            $sub_staff_info_id = $sub_result['data']['sub_staff_info_id'] ?? 0;
                            $this->syncMsSupportApply($apply_detail, $sub_staff_info_id);
                        }
                        if (!empty($sub_staff_info_id)) {
                            //新增子账号班次
                            $shiftParam['sub_staff_info_id']     = $sub_staff_info_id;
                            $shiftParam['employment_begin_date'] = $today;
                            $this->createSubStaffShift($shiftParam);

                            //支援给子账号发送 完善简历消息
                            $sendResult = (new ResumeServer())->sendResumeMessage($apply_detail['staff_info_id'], $sub_staff_info_id, $today);
                            $this->logger->write_log([
                                'function' => 'StaffSupportStoreServer-setProperty-sendResumeMessage',
                                'params' => [$apply_detail['staff_info_id'], $sub_staff_info_id, $today],
                                'result' => $sendResult
                            ], 'info');
                        }
                    }
                }

                if($today >= $apply_detail['employment_begin_date']) {
                    $shiftParam['employment_begin_date'] = $today;
                    $update_field = ['sub_staff_info_id' => $sub_staff_info_id, 'support_status' => 1];
                    if($today <= $apply_detail['employment_end_date']) {
                        $update_field = ['sub_staff_info_id' => $sub_staff_info_id, 'support_status' => 2, 'actual_begin_date' => $today];
                    }
                    if($today > $apply_detail['employment_end_date']) {
                        $update_field = ['sub_staff_info_id' => $sub_staff_info_id, 'support_status' => 3];
                    }

                    $this->getDI()->get('db')->updateAsDict(
                        'hr_staff_apply_support_store',
                        $update_field,
                        'id = ' . $apply_detail['id']
                    );
                }

                if($today <= $apply_detail['employment_end_date']) {
                    //只要支援没结束 把主账号班次替换了
                    $this->createMasterStaffShift($shiftParam);
                    //发送消息
                    $detail = $this->getStaffApplySupportDetail($auditId);
                    $staff_info_id = $detail['staff_info_id'];
                    //获取收信人语言环境
                    $lang = (new StaffServer)->getLanguage($staff_info_id);
                    //获取语言
                    $t = $this->getTranslation($lang);
                    $message_title = $t->_('staff_support_support_store');

                    $staff_info = $this->getStaffInfo($staff_info_id);
                    $staff_job_title = $staff_info['job_title'] ?? 0;
                    $message_template = 'store_support_staff_message_v2';
                    if(in_array($staff_job_title, [enums::$job_title['branch_supervisor'], enums::$job_title['assistant_branch_supervisor']]) && $detail['job_title_id'] == enums::$job_title['van_courier']) {
                        $message_template = 'store_support_staff_message_v2_th';
                    }

                    $content = $t->_($message_template,
                        [
                            'support_store' => $detail['store_name'],
                            'employment_begin_date' => $detail['employment_begin_date'],
                            'employment_end_date' => $detail['employment_end_date'],
                            'shift' => $detail['work_shift']
                        ]
                    );

                    $id = time() . $staff_info_id . rand(1000000, 9999999);
                    $param['staff_users'] = array($staff_info_id);//数组 多个员工id
                    $param['message_title'] = $message_title;
                    $param['message_content'] = addslashes("<div style='font-size: 30px'>" . $content . "</div>");;
                    $param['staff_info_ids_str'] = $staff_info_id;
                    $param['id'] = $id;
                    $param['category'] = -1;
                    try {
                        $this->getDI()->get('logger')->write_log([
                            'function' => 'StaffSupportStoreServer-setProperty',
                            'params' => $param
                        ], 'info');
                        $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $lang));
                        $bi_rpc->setParams($param);
                        $res = $bi_rpc->execute();
                        $this->getDI()->get('logger')->write_log([
                            'function' => 'StaffSupportStoreServer-setProperty',
                            'message' => '消息发送成功',
                            'params' => $param,
                            'result' => $res
                        ], 'info');
                    } catch (Exception $e) {
                        $this->getDI()->get('logger')->write_log([
                            'function' => 'StaffSupportStoreServer-setProperty',
                            'message' => $e->getMessage(),
                            'line' => $e->getLine(),
                            'file' => $e->getFile()
                        ], 'error');
                    }
                }
            }
        }
    }

    /**
     * 创建子账号
     * @param $apply_id
     * @return bool
     */
    /*
    public function createSubStaff($apply_id) {
        try {
            $apply_detail = $this->getStaffApplySupportDetail($apply_id);
            if(empty($detail)) {
                return false;
            }
            $staff_info = (new StaffServer())->get_staff($apply_detail['staff_info_id']);

            $staff_arr = [
                'name'               => $staff_info['staff_name'],
                'sex'                => $staff_info['sex'],
                'identity'           => $staff_info['identity'],
                'mobile'             => $staff_info['mobile'],
                'personal_email'     => $staff_info['personal_email'],
                'job_title'          => $apply_detail['job_title_id'],//职位id
                'sys_store_id'       => $apply_detail['store_id'],//网点id
                'sys_department_id'  => $staff_info['sys_department_id'],//部门id
                'node_department_id' => $staff_info['node_department_id'],
                'formal'              => 1,
                'state'               => 1,
                'is_sub_staff'        => 1,
                'hire_date'           => date('Y-m-d',strtotime($apply_detail['employment_begin_date'])),
                'staff_car_no'        => $staff_info['car_no'],
                'newMasterStaff' => $apply_detail['staff_info_id'],
                'leave_date' => null
            ];

            $bi_rpc = (new ApiClient('hr_rpc', '', 'create_sub_staff_info', 'zh-CN'));
            $bi_rpc->setParams($staff_arr);
            $res = $bi_rpc->execute();
            if(isset($res['code']) && $res['code'] == 1) {
                $sub_staff_info_id = $res['data']['staff_info_id'] ?? 0;
                $this->getDI()->get('db')->updateAsDict(
                    'hr_staff_apply_support_store',
                    ['sub_staff_info_id' => $sub_staff_info_id],
                    'id = '. $apply_id
                );
                return true;
            } else {
                $this->wLog(['message' => '创建子账号失败', 'params' => $staff_arr,'result' => $res], 'error');
                return false;
            }
        } catch (Exception $e) {
            $this->wLog(['message' => $e->getMessage(),'file' => $e->getFile(),'line' => $e->getLine(), 'params' => $apply_id], 'error');
            return false;
        }
    }
    */

    /**
     * 生成概要信息
     * @param int $auditId
     * @param $user
     * @return mixed|void
     */
    public function genSummary(int $auditId, $user)
    {
        $info  = $this->getStaffApplySupportDetail($auditId);
        if (!empty($info)) {
            $job_title_name = UC('storeSupport')['jobTitles'][$info['job_title_id']] ?? '';
            $param = [
                [
                    'key'   => "store_support_staff_job_title",
                    'value' => $job_title_name
                ],
                [
                    'key'   => "store",
                    'value' => $info['store_name']
                ]
            ];
        }
        return $param ?? [];
    }

    /**
     * 获取审批流参数
     * @param $auditId
     * @param $user
     * @param null $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $auditInfo = HrStaffApplySupportStoreModel::findFirst($auditId);
        if (empty($auditInfo)) {
            return false;
        }
        $staff_info = $this->staff->checkoutStaff($auditInfo->staff_info_id);

        $store_apply_support_detail = HrStoreApplySupportModel::findFirst([
            'conditions' => "serial_no = :serial_no:",
            'bind' => [
                'serial_no' => $auditInfo->store_apply_support_serial_no,
            ],
        ]);
        if(empty($store_apply_support_detail)) {
            return false;
        }

        $apply_staff_store = (new SysStoreServer())->getStoreRegionPiece($staff_info['organization_id']);
        $is_same_piece = 0;
        if($apply_staff_store['manage_piece'] == $store_apply_support_detail->store_piece_id) {
            $is_same_piece = 1;
        }

        $storeInfo = \FlashExpress\bi\App\Models\backyard\SysStoreModel::findFirst([
            "id = :id:",
            "bind" => [
                "id" => $staff_info['organization_id']
            ],
            'columns' => 'category'
        ]);


        return [
            'category'      => !empty($storeInfo) ? $storeInfo->category : 0,
            'job_title'     => $staff_info['job_title'],
            'is_same_piece' => $is_same_piece, //申请人网点所在片区是否和支援网点在同一片区
        ];
    }

    //获取可申请的网点列表
    public function getStoreSupportList($paramIn) {
        $store_name      = $this->processingDefault($paramIn, 'store_name');
        $employment_date = $this->processingDefault($paramIn, 'employment_date');
        $staff_job_title = $this->processingDefault($paramIn['userinfo'], 'job_title', 2);
        $store_id        = $this->processingDefault($paramIn['param'], 'store_id');
        $staff_info_id   = $this->processingDefault($paramIn, 'staff_id');

        $builder = $this->modelsManager->createBuilder();
        $builder->from(HrStoreApplySupportModel::class);
        $builder->columns('*');

        $builder->where('status = 2');
        $builder->inWhere('support_status',[HrStoreApplySupportModel::SUPPORT_STATUS_RECRUIT]);

        //todo 申请支援改动点(3-3)
        if (!in_array($staff_job_title, [
            enums::$job_title['regional_manager'],
            enums::$job_title['district_manager'],
            enums::$job_title['branch_supervisor'],
            enums::$job_title['dc_officer'],
            enums::$job_title['assistant_branch_supervisor'],
            enums::$job_title['cdc_officer'],
            enums::$job_title['cdc_supervisor'],
            HrJobTitleEnums::JOB_TITLE_SENIOR_BRANCH_SUPERVISOR,
        ])) {
            $builder->andWhere('job_title_id = :job_title_id:', ['job_title_id' => $staff_job_title]);
            $builder->andWhere('store_id != :store_id:', ['store_id' => $store_id]);
        }

        if ($staff_job_title == enums::$job_title['cdc_officer']) {
            $builder->andWhere('job_title_id in ({job_title_ids:array})',
                ['job_title_ids' => [enums::$job_title['van_courier'], enums::$job_title['bike_courier'], enums::$job_title['dc_officer']]]);
            $builder->andWhere('store_id != :store_id:', ['store_id' => $store_id]);
        }
        if (in_array($staff_job_title, [enums::$job_title['cdc_supervisor'], HrJobTitleEnums::JOB_TITLE_SENIOR_BRANCH_SUPERVISOR])) {
            $builder->andWhere('job_title_id in ({job_title_ids:array})',
                ['job_title_ids' => [enums::$job_title['van_courier'], enums::$job_title['bike_courier'], enums::$job_title['dc_officer'],enums::$job_title['branch_supervisor']]]);
            $builder->andWhere('store_id != :store_id:', ['store_id' => $store_id]);
        }

        if(!empty($store_name)) {
            $builder->andWhere('store_name like :store_name:', ['store_name' => '%' . $store_name . '%']);
        }

        if(!empty($employment_date)) {
            $builder->andWhere('employment_begin_date >= :employment_date:', ['employment_date' => $employment_date]);
        } else {
            $add_hour = $this->getDI()['config']['application']['add_hour'];
            $builder->andWhere('employment_begin_date > :employment_date:', ['employment_date' => gmdate("Y-m-d", time() + $add_hour*3600)]);
        }

        $store_support_list = $builder->getQuery()->execute()->toArray();

        $list = [];
        if(!empty($store_support_list)) {
            $store_detail = SysStoreModel::findFirst([
                'conditions' => "id = :store_id:",
                'bind' => [
                    'store_id' => $store_id
                ],
            ]);
            if(!empty($store_detail)) {
                $current_lat_lng = [
                    'lat' => $store_detail->lat,
                    'lng' => $store_detail->lng,
                ];
            } else {
                $current_lat_lng = [
                    'lat' => 0,
                    'lng' => 0,
                ];
            }

            $store_ids = array_column($store_support_list, 'store_id');
            $store_list = SysStoreModel::find([
                'conditions' => "id in ({store_ids:array})",
                'bind' => [
                    'store_ids' => $store_ids
                ],
            ])->toArray();

            $store_list = array_column($store_list, null, 'id');

            $region_list = SysManageRegionModel::find([
                'conditions' => "deleted = 0"
            ])->toArray();
            $region_list = array_column($region_list, null, 'id');

            $piece_list = SysManagePieceModel::find([
                'conditions' => "deleted = 0"
            ])->toArray();
            $piece_list = array_column($piece_list, null, 'id');

            $apply_support_ids = array_column($store_support_list,'serial_no');
            $support_query = $this->modelsManager->createBuilder()
                                ->from(HrStaffApplySupportStoreModel::class)
                                ->columns('store_apply_support_serial_no,count(1) as count')
                                ->inWhere('store_apply_support_serial_no',$apply_support_ids)
                                ->inWhere('status',[1, 2])
                                ->groupBy('store_apply_support_serial_no')
                                ->getQuery()
                                ->execute()
                                ->toArray();
            $support_query = array_column($support_query, null, 'store_apply_support_serial_no');

            foreach ($store_support_list as $key => $value) {
                $difficult_phone_first = $store_list[$value['store_id']]['difficult_phone_first'] ?? '';
                if(!empty($difficult_phone_first)) {
                    $store_phone = $difficult_phone_first;
                } else {
                    $difficult_phone_second = $store_list[$value['store_id']]['difficult_phone_second'] ?? '';
                    if (!empty($difficult_phone_second)) {
                        $store_phone = $difficult_phone_second;
                    } else {
                        $store_phone = $store_list[$value['store_id']]['difficult_phone_second'] ?? '';
                    }
                }

                $store_region_id = $store_list[$value['store_id']]['manage_region'] ?? '0';
                $store_piece_id = $store_list[$value['store_id']]['manage_piece'] ?? '0';

                $store_lat_lng = [
                    'lat' => $store_list[$value['store_id']]['lat'] ?? '0',
                    'lng' => $store_list[$value['store_id']]['lng'] ?? '0',
                ];

                $distance = 0;
                if(!empty($current_lat_lng['lat']) && !empty($current_lat_lng['lng']) && !empty($store_lat_lng['lat']) && !empty($store_lat_lng['lng'])) {
                    $distance = $this->getCoordinateDistanc($current_lat_lng, $store_lat_lng);
                }
                $already_count = $support_query[$value['serial_no']]['count'] ?? 0;
                $last_count = $value['final_audit_num'] - $already_count;
                if($last_count <= 0) {
                    continue;
                }
                $list[] = [
                    'serial_no'     => $value['serial_no'],
                    'store_id'      => $value['store_id'],
                    'store_name'    => $value['store_name'],
                    'store_address' => $store_list[$value['store_id']]['detail_address'] ?? '',
                    'distance'      => $distance,
                    'distance_text' => ($distance / 1000) . 'KM',
                    'employment_begin_date' => $value['employment_begin_date'],
                    'employment_end_date' => $value['employment_end_date'],
                    'support_date'  => $value['employment_begin_date'] . ' - ' . $value['employment_end_date'],
                    'shift'         => $value['shift_start'] . '-' . $value['shift_end'],
                    'job_title'     => UC('storeSupport')['jobTitles'][$value['job_title_id']] ?? '',
                    'demand_num'    => $last_count,
                    'store_phone'   => $store_phone,
                    'store_manage_region' => $store_region_id,
                    'store_manage_region_name' => $region_list[$store_region_id]['name'] ?? '',
                    'store_manage_piece'  => $store_piece_id,
                    'store_manage_piece_name'  => $piece_list[$store_piece_id]['name'] ?? '',
                    'employment_days' => $value['employment_days'],
                ];
            }

            $distance_list = array_column($list, 'distance');
            array_multisort($distance_list, SORT_ASC, $list);
        }
        return $list;
    }

    //获取两个坐标直线距离
    public function getCoordinateDistanc($begin,$end) {
        $earth_radius = 6371000;   //approximate radius of earth in meters
        $dLat = deg2rad($begin['lat'] - $end['lat']);
        $dLon = deg2rad($begin['lng'] - $end['lng']);
        /*
          Using the
          Haversine formula
          http://en.wikipedia.org/wiki/Haversine_formula
          http://www.codecodex.com/wiki/Calculate_Distance_Between_Two_Points_on_a_Globe
          验证：百度地图  http://developer.baidu.com/map/jsdemo.htm#a6_1
          calculate the distance
        */
        $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($end['lat'])) * cos(deg2rad($begin['lat'])) * sin($dLon/2) * sin($dLon/2);
        $c = 2 * asin(sqrt($a));
        $d = round($earth_radius * $c);

        return $d;
    }

    //判断员工是否已经招满
    public function isStaffSupportFull($serial_no, $demand_num) {
        $list = HrStaffApplySupportStoreModel::find([
            'conditions' => "store_apply_support_serial_no = :serial_no: and status = 2",
            'bind' => [
                'serial_no' => $serial_no
            ]
        ])->toArray();

        if(count($list) >= $demand_num) {
            return true;
        }
        return false;
    }

    //判断是否申请过该工单
    public function isApplySerialNo($serial_no, $staff_info_id) {
        $applying_detail = HrStaffApplySupportStoreModel::find([
            'conditions' => "staff_info_id = :staff_info_id: and status in (1,2) and store_apply_support_serial_no = :store_apply_support_serial_no: and support_status != 4",
            'bind' => [
                'store_apply_support_serial_no' => $serial_no,
                'staff_info_id' => $staff_info_id
            ],
        ])->toArray();

        if(!empty($applying_detail)) {
            return true;
        }
        return false;
    }

    /*
    //判断是否与其他工单时间冲突
    public function validateConflict($employment_begin_date, $staff_info_id) {
        $detail = HrStaffApplySupportStoreModel::find([
            'conditions' => "staff_info_id = :staff_info_id: and status in (1,2) and employment_end_date >= :employment_begin_date: and support_status != 4",
            'bind' => [
                'employment_begin_date' => $employment_begin_date,
                'staff_info_id' => $staff_info_id
            ],
        ])->toArray();
        if(!empty($detail)) {
            return true;
        }
        return false;
    }
    */
}