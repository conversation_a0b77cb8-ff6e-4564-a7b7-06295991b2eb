<?php

namespace FlashExpress\bi\App\Modules\Ph\Controllers;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Modules\Ph\Server\OsStaffServer;
use Exception;
use FlashExpress\bi\App\Server\SettingEnvServer;

class OsController extends BaseController
{
    protected $os;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 添加外协员工申请
     * @param int    job_id             外协职位
     * @param string employment_date    雇佣日期
     * @param int    employment_days    雇佣天数
     * @param int    shift_id           班次ID
     * @param int    demend_num         申请人数
     * @param string reason             申请原因
     * @return json
     */
    public function addOsStaffAction()
    {
        //[1]入参校验
        $paramIn                               = $this->paramIn;
        $paramIn['param']                      = $this->userinfo;
        $paramIn['param']['position_category'] = $this->userinfo['positions'];
        $paramIn['param']['store_id']          = $this->userinfo['organization_id'];
        $paramIn['staff_id']                   = $this->userinfo['staff_id'];
        $paramIn['organization_id']            = $this->userinfo['organization_id'];
        $paramIn['organization_type']          = $this->userinfo['organization_type'];
        $logger                                = $this->getDI()->get('logger');

        //[2]数据验证

        if (!isset($paramIn['os_type']) ||
            empty($paramIn['os_type']) ||
            !in_array($paramIn['os_type'], [
                enums::$os_staff_type['normal'],
                enums::$os_staff_type['long_term'],
                enums::$os_staff_type['motorcade'],
            ])) {
            throw new \Exception("'os_type' invalid input");
        }

        $hubOsRoles = explode(',', (new SettingEnvServer())->getSetValFromCache('hub_os_roles'));


        switch ($paramIn['os_type']) {
            case enums::$os_staff_type['normal']:
                //hub 外协工单，分拨经理 提交申请校验规则
                if (array_intersect($hubOsRoles, $this->userinfo['positions'])) {
                    $hubOsSelectRoles = (new SettingEnvServer())->getSetValFromCache('hub_os_select_roles');
                    $validation_sub   = [
                        'job_id'          => "Required|IntIn:{$hubOsSelectRoles}|>>>:" . $this->getTranslation()->_('3008'),
                        'employment_days' => 'Required|IntIn:1',
                        'demend_num'      => 'Required|IntGeLe:1,200|>>>:' . "'demend_num' invalid input",
                    ];
                } else {
                    $validation_sub = [
                        "job_id"          => "Required|IntIn:13,110,111,807,452,812,1000,1194,1652,300,37|>>>:" . $this->getTranslation()->_('3008'),
                        "employment_days" => "Required|IntGeLe:1,7",
                        "demend_num"      => "Required|IntGeLe:1,50|>>>:" . "'demend_num' invalid input",
                    ];
                    //如果是短期外协 807 Hub Operator 职位 增加人数到200
                    if ($paramIn['job_id'] == 807 || $paramIn['job_id'] == enums::$job_title['warehouse_staff']){//https://flashexpress.feishu.cn/wiki/CTekwMRqFioCIRkNOpAcEQOQnQT
                        $validation_sub['demend_num'] = "Required|IntGeLe:1,200|>>>:" . "'demend_num' invalid input";
                    }
                }
                break;
            case enums::$os_staff_type['long_term']:
                $validation_sub = [
                    "job_id"          => "Required|IntIn:807,300|>>>:" . $this->getTranslation()->_('3008'),
                    "employment_days" => "Required|IntGeLe:90,365|>>>:" . $this->getTranslation()->_('err_msg_more_days'),
                    "demend_num"      => "Required|IntGeLe:1,200|>>>:" . "'demend_num' invalid input",//申请人数 扩到200
                ];
                if ($paramIn['job_id'] == enums::$job_title['warehouse_staff']){
                    $validation_sub['employment_days'] = "Required|IntGeLe:30,365|>>>:" . $this->getTranslation()->_('os_staff_err_1');
                }
                break;
            case enums::$os_staff_type['motorcade']:
                $validation_sub = [
                    "job_id"          => "Required|IntIn:110|>>>:" . $this->getTranslation()->_('3008'),
                    "employment_days" => "Required|IntGeLe:1,7",
                    "demend_num"      => "Required|IntGeLe:1,100|>>>:" . "'demend_num' invalid input",
                ];
                break;
            default:
                $validation_sub = [];
                break;
        }

        //[3]数据验证
        //短期外协的可选雇用日期为从申请日期的第二天起+7天
        //长期外协的可选雇用日期为从申请日期的第二天起+90日
        $dateFrom = date("Y-m-d", time() + 86400);
        if ($paramIn['os_type'] == enums::$os_staff_type['normal']) {
            $dateFrom = date("Y-m-d", time());
        }
        $dateTo = $paramIn['os_type'] == enums::$os_staff_type['normal']
            ? date("Y-m-d", time() + 8 * 86400)
            : date("Y-m-d", time() + 91 * 86400);
        $validations = [
            "employment_date" => "Required|DateFromTo:{$dateFrom},{$dateTo}|>>>:" . $this->getTranslation()->_('err_msg_invalid_date'),
            //"shift_id"        => "Required|Int",
            "reason_type"     => "Required|IntGeLe:0,8",
        ];

        if (in_array($paramIn['reason_type'], [0, 2])) { //离职或其他原因需验证reason字段[原因备注字段]
            $valid       = [
                "reason" => "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1019'),
            ];
            $validations = array_merge($validations, $valid);
        }

        //hub 外协工单，分拨经理 提交申请校验规则
        if (array_intersect($hubOsRoles, $this->userinfo['positions'])) {
            $dateFrom                       = date('Y-m-d');
            $dateTo                         = date('Y-m-d', strtotime('+6 day'));
            $validations['employment_date'] = "Required|DateFromTo:{$dateFrom},{$dateTo}|>>>:" . $this->getTranslation()->_('today_tomorrow_date_error');
            $validations['company_id']      = "Required|Int|>>>:'company_id' invalid input";

            //新增班次开始时间 班次时长校验
            $validations['shift_begin_time'] = "Required|Str|>>>:" . $this->getTranslation()->_('os_shift_begin_time_error');
            $validations['shift_duration']   = "Required|IntGeLe:5,9|>>>:" . $this->getTranslation()->_('os_shift_duration_error');
            $validations['need_remark']      = "StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('os_need_remark_error');
        } else {
            $validations['shift_id'] = "Required|Int";
        }

        //外协类型
        $validations['hire_os_type'] = "IntIn:11,12";
        $this->validateCheck($this->paramIn, array_merge($validations, $validation_sub));

        //[4]业务处理
        $returnArr = (new OsStaffServer($this->lang, $this->timezone))->addOsStaffUseLock($paramIn);


        //[3]数据返回
        return $this->jsonReturn($returnArr);
    }


    /**
     * 获取班次、申请职位、申请原因列表；所属部门；
     */
    public function getShiftListAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        //[2]业务处理
        $returnArr = (new OsStaffServer($this->lang, $this->timezone))->getRequestList($paramIn);

        //[3]数据返回
        return $this->jsonReturn($this->checkReturn(['data' => ['dataList' => $returnArr]]));
    }

    /**
     * 获取可申请职位下拉列表
     */
    public function getOsJobTitleListAction()
    {
        try {
            //[1]传入参数
            $paramIn                = $this->paramIn;
            $paramIn['userinfo']    = $this->userinfo;

            $validations = [
                "type"      => "Required|IntIn:1,2,3",
            ];
            $this->validateCheck($this->paramIn, $validations);

            $returnArr = (new OsStaffServer($this->lang, $this->timezone))->getOsJobTitleList($paramIn);
            return  $this->jsonReturn(self::checkReturn(['data' => $returnArr]));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("OsController:shortTermApplyAction:" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }


    /**
     * 好像废弃 没用了 但是前端说还在用 泰国和马来 不用这个名
     * @description: 获取外协类型
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/14 17:13
     */
    public function getHireOsTypeListAction(){
        try {
            //[1]传入参数
            $paramIn                = $this->paramIn;
            $paramIn['userinfo']    = $this->userinfo;

            $returnArr = (new OsStaffServer($this->lang, $this->timezone))->getHireOsTypeList($paramIn);
            return  $this->jsonReturn(self::checkReturn($returnArr));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("OsController:getHireOsTypeListAction:" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }
}