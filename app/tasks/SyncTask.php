<?php

use FlashExpress\bi\App\library\Enums;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\Server\XxljobTaskServer;

class SyncTask extends BaseTask
{

    /**
     * @var
     */
    private $client;

    /**
     * 获得backyard token
     * @return mixed|string
     */

    public function getToken()
    {
        $token = $this->redisLib->get('by_auth_token');
        $token = null;
        if (empty($token)) {
            $params =
                [
                    'client_id' => 'WHGdsadedRc',
                    'client_secret' => 'zdeWgkineiEKdaixn',
                    'grant_type' => 'client_credentials',
                ];
            $this->client = new RestClient('by');
            $res = $this->client->execute(RestClient::METHOD_POST, '/api/auth/token', $params);
            if ($res) {
                $token = $res['access_token'];
                $expire = $res['expires_in'];
                $this->redisLib->set('by_auth_token', $token, $expire-5*3600);
            }
        }
        return $token;
    }

    /**
     * 同步考勤
     * @param $params
     * @param int $flag 1=考勤，2请假,3出差，4加班
     * @return false|mixed|string
     */
    public function send_to_backyard($acrtion = '', $params)
    {
        $token = $this->getToken();
        if (empty($token)) {
            $this->logger->write_log('RestClient error=get token empty', 'warning');
            echo printf('RestClient error=get token empty %s', date("Y-m-d H:i:s")).PHP_EOL;
            return false;
        }
        $path = '/api/sync_job/'.$acrtion;
        $headers = ['Authorization'=> 'Bearer ' . $token];
        $res = $this->client->execute(RestClient::METHOD_POST, $path, $params, $headers);
        return $res;
    }

    public function xxljobAction($params)
    {
        try {
            $data = null;
            if(empty($params[0])){
                exit('params empty');
            }
            $data = $params[0] ;
            $d = $this->send_to_backyard('xxljobtask',['data'=>$data]);
            if(empty($d['data'])){
                echo 'data empty';
            }
            $s = (new XxljobTaskServer())->JobSave($d['data']);
            
        }catch (\Exception $e){
            $this->logger->write_log('xxljob '.$e->getMessage());
        }
    }

}