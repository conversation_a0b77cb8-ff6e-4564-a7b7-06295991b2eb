<?php


use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Server\AuditListServer;

class AuditSummaryUpdateTask extends RocketMqBaseTask
{
    //php app/cli.php audit-summary-update main
    public function initialize()
    {
        $this->tq = "audit-list-update";//业务上先择的消费rmq的内容
        parent::initialize();
    }

    /**
     * mainAction
     * @param $msgBody
     * @return bool
     */
    protected function processOneMsg($msgBody): bool
    {
        $messageBody = json_decode($msgBody, true);
        if (empty($messageBody)) {
            return false;
        }

        $this->logger->write_log(['AuditSummaryUpdateTask' => $msgBody], 'info');

        $msgData    = json_decode($messageBody['data'], true);
        $auditType  = $msgData['audit_type'];
        $auditValue = $msgData['audit_value'];
        $updateList = $msgData['update_list'];
        try {
            echo '有数据:'. json_encode($messageBody['data'], JSON_UNESCAPED_UNICODE) .PHP_EOL;
            (new AuditListServer($this->lang, $this->timezone))->updateAuditListSummary($auditType, $auditValue, $updateList);
        } catch (\Exception $e) {
            echo $e->getMessage().PHP_EOL;
        }
        return true;
    }

    //php app/cli.php audit-summary-update aa
    public function aaAction()
    {
        $insertParams = [
            'audit_type' => AuditListEnums::APPROVAL_TYPE_HC,
            'audit_value'=> 59705,
            'update_list' => [
                'demandnumber' => 1,
            ],
        ];
        $rmq  = new RocketMQ('audit-list-update');
        $rmq->setType(RocketMQ::TAG_AUDIT_LIST_SUMMARY_UPDATE);
        $rid = $rmq->sendMsgByTag($insertParams);
        echo $rid.PHP_EOL;
    }

    public function fixAction($args)
    {
        if (empty($args)) {
            $date = '2024-09-01';
        } else {
            $date = $args[0];
        }
        $hcInfo = HrHcModel::find([
            'conditions' => 'createtime >= :date:',
            'bind'       => [
                'date'  => $date,
            ],
            'columns'    => 'hc_id,demandnumber'
        ])->toArray();

        $re = new \FlashExpress\bi\App\Repository\ApplyRepository();
        foreach ($hcInfo as $item) {
            $apply = \FlashExpress\bi\App\Models\backyard\AuditApplyModel::findFirst([
                'conditions' => 'biz_type = 6 and biz_value = :value:',
                'bind' => [
                    'value' => $item['hc_id'],
                ],
                'columns'    => 'summary'
            ]);
            if (empty($apply)) {
                continue;
            }
            $summaryArray = json_decode($apply->summary, true);
            if (empty($summaryArray)) {
                continue;
            }
            $rmq  = new RocketMQ('audit-list-update');
            foreach ($summaryArray as $key => $value) {
                if ($value['key'] == 'demandnumber' && $item['demandnumber'] != $value['value']) {
                    $insertParams = [
                        'audit_type' => AuditListEnums::APPROVAL_TYPE_HC,
                        'audit_value'=> $item['hc_id'],
                        'update_list' => [
                            'demandnumber' => $item['demandnumber'],
                        ],
                    ];
                    $rmq->setType(RocketMQ::TAG_AUDIT_LIST_SUMMARY_UPDATE);
                    $rid = $rmq->sendMsgByTag($insertParams);
                    echo $item['hc_id'].'-',$rid.PHP_EOL;
                }
            }
        }
    }
}