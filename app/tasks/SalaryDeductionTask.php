<?php

use FlashExpress\bi\App\library\ApiClient;

/**
 * Author: Bruce
 * Date  : 2022-07-19 17:47
 * Description:
 */



class SalaryDeductionTask extends BaseTask
{
    public function mainAction()
    {
        // 获取上级
        $sql = "--
                        select 
                               id, staff_info_id, staff_name, deduction_amount
                        from 
                                hr_staff_salary_deduciton
                        where 
                                send_status = 0";
        $staffInfos = $this->getDI()->get("db_rby")->fetchAll($sql);

        foreach ($staffInfos as $oneStaff) {
            $param['staff_users'] = [$oneStaff['staff_info_id']];//数组 多个员工id
            $param['message_title'] = 'เรื่อง การหักเงินเดือนส่งกองทุนเงินให้กู้ยืมเพื่อการศึกษา(กยศ./กรอ.)';
            $param['message_content'] = json_encode($oneStaff, JSON_UNESCAPED_UNICODE);
            $param['staff_info_ids_str'] = $oneStaff['staff_info_id'];
            $param['id'] = time() . $oneStaff['staff_info_id'] . rand(1000000, 9999999);
            $param['category'] = 74;
            $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', 'th'));
            $bi_rpc->setParams($param);
            $res = $bi_rpc->execute();
            $this->getDI()->get('logger')->write_log([
                'function' => 'SalaryDeductionTask',
                'message' => '消息发送成功',
                'params' => $param,
                'result' => $res
            ], 'info');
            if($res['result']['code'] == 1) {
                $this->getDI()->get("db")->updateAsDict('hr_staff_salary_deduciton', ["send_status" => 1],                                                [
                    "conditions" => " id = ? ",
                    "bind"       => [$oneStaff['id']]
                ]);
            }
        }
        exit('send success');
    }
}