<?php

use FlashExpress\bi\App\Server\HrStaffRenewContractServer;
use FlashExpress\bi\App\library\DateHelper;

class HrStaffRenewContractTask extends BaseTask
{
    /**
     * 任务执行时间：每天早上9：00执行
     * 合同结束日期还剩30天时系统自动发起续签流程，北京时间早9:00开始发送
     * 查找数据 发送审批
     * @return void
     */
    public function add_applyAction()
    {
        echo 'begin' . PHP_EOL;
        $date   = gmdate('Y-m-d', strtotime('+30 days'));
        echo '业务日期：' . $date . PHP_EOL;
        $server = new HrStaffRenewContractServer($this->lang, $this->timezone);
        $result = $server->createStaffRenewContractApply(['date' => $date]);
        echo json_encode($result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
        echo 'end' . PHP_EOL;
    }

    /**
     * 任务执行时间：每天早上8：50执行
     * 第八天北京时间早8：50发消息AM和DM。文案三
     * 超时关闭
     * @return void
     */
    public function time_outAction()
    {
        echo 'begin' . PHP_EOL;
        $date = DateHelper::localToUtc(date('Y-m-d 00:00:00', strtotime('-7 days')));
        echo '业务日期：' . $date . PHP_EOL;
        $server = new HrStaffRenewContractServer($this->lang, $this->timezone);
        $result = $server->timeOutCloseRenewContractApply(['date' => $date]);
        echo json_encode($result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
        echo 'end' . PHP_EOL;
    }

    /**
     * 任务执行时间：每天早上8：50执行
     * 第八天北京时间早8：50发消息AM和DM。文案三
     * 超时关闭
     *
     * php app/cli.php hr_staff_renew_contract time_out_v2
     *
     * @return void
     */
    public function time_out_v2Action($args = [])
    {
        if (empty($args[0])) {
            $date = date('Y-m-d');
        } else {
            $date = $args[0];
        }
        echo 'begin' . PHP_EOL;
        echo '业务日期：' . $date . PHP_EOL;
        $server = new HrStaffRenewContractServer($this->lang, $this->timezone);
        $result = $server->timeOutCloseRenewContractApplyV2(['date' => $date]);
        echo json_encode($result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
        echo 'end' . PHP_EOL;
    }

    /**
     * 任务执行时间：每天早上9点执行
     * 如果前四天没有审批，从第五天开始至第七天，每天北京时间早9:00提醒审批人。文案一；并发消息AM和DM。文案二
     * @return void
     */
    public function pending_approval_send_messageAction()
    {
        echo 'begin' . PHP_EOL;
        $date = DateHelper::localToUtc(date('Y-m-d 00:00:00', strtotime('-3 days')));
        echo '业务日期：' . $date . PHP_EOL;
        $server = new HrStaffRenewContractServer($this->lang, $this->timezone);
        $result = $server->renewContractPendingApprovalSendMessage(['date' => $date]);
        echo json_encode($result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
        echo 'end' . PHP_EOL;
    }
}