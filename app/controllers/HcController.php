<?php
namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationBkModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Server\HcServer;
use FlashExpress\bi\App\Server\SalaryServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SysDepartmentServer;
use FlashExpress\bi\App\Server\SysServer;
use FlashExpress\bi\App\Server\SysStoreServer;

class HcController extends Controllers\ControllerBase
{
    /**
     * @var HcServer
     */
    protected $server;

    public function initialize()
    {
        parent::initialize();
        $this->server = $this->class_factory("HcServer",$this->lang, $this->timezone);
    }

    /**
     * 枚举项
     * @return null
     */
    public function enumAction()
    {
        $paramIn = $this->paramIn;
        $data = (new HcServer($this->lang, $this->timezone))->getEnums($paramIn);
        $this->jsonReturn($this->checkReturn(['data'=>$data]));
    }


    /**
     * 获取jd列表
     */
    public function getJdListAction()
    {
        $param['method'] = 'jd_list';
        $param['param']  = [
            'user_info' => $this->userinfo,
        ];
        $data = $this->HcBridge($param);
        $this->jsonReturn($this->checkReturn($data));
    }

    /**
     * 获取jd详情
     * @param int job_id  Jd id
     * @return array
     */
    public function getJdInfoAction()
    {
        //[1]参数定义
        $paramIn = $this->paramIn;

        //[2]数据验证
        $validations = [
            "job_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        $jobId = $this->processingDefault($paramIn, 'job_id');
        $param['method'] = 'jd_info';
        $param['param']  = ['job_id' => $jobId];

        //[3]请求接口
        $data = $this->HcBridge($param);

        $this->jsonReturn($this->checkReturn($data));
    }

    /**
     * 获取dj list
     */
    public function getJdPageAction()
    {
        //[1]参数定义
        $paramIn = $this->paramIn;

        //[2]数据验证
        $validations = [
            "page_num"  => "Required|Int",
            "page_size" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        $pageNum  = $this->processingDefault($paramIn, 'page_num', 2);
        $pageSize = $this->processingDefault($paramIn, 'page_size', 2);
        $param['method'] = 'jd_page_list';
        $param['param']  = [
            'user_info' => $this->userinfo,
            'page_num'  => $pageNum,
            'page_size' => $pageSize
        ];

        //[3]请求接口
        $data = $this->HcBridge($param);

        //获取部门列表

        $this->jsonReturn($this->checkReturn(['data' => ['dataList' => $data['data'] ?? []]]));
    }

    /**
     * 获取部门关联的职位
     */
    public function getJobTitleListAction()
    {
        //[1]参数定义
        $paramIn = $this->paramIn;

        //[2]数据验证
        $validations = [
            "department_id"  => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        $return = (new HcServer($this->lang, $this->timezone))->getJobTitleList($paramIn);

        $this->jsonReturn($return);
    }

    /**
     * 添加HC
     * @return void
     * @throws ValidationException
     */
    public function addHcInfoAction()
    {
        //[1]参数定义
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['userinfo'] = $this->userinfo;

        //[2]数据验证
        $validations = [
            "department_id"     => "Required|IntGt:0",
            "worknode_id"       => "Required|Str",
            "demandnumber"      => "Required|IntGe:1",
            "expirationdate"    => "Required|Date",
            "remarks"           => "Required|StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('4014'),
            "reason"            => "Required|StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('4014'),
            "job_id"            => "Required|IntGt:0|>>>:" . $this->getTranslation()->_('jobtransfer_0040'),
            "job_title_id"      => "Required|IntGt:0|>>>:" . $this->getTranslation()->_('jobtransfer_0042'),
            "priority_id"       => "Required|IntGeLe:1,4|>>>:" . $this->getTranslation()->_('jobtransfer_0041'),
            "type"              => "Required|IntIn:1,2",
            "reason_type"       => "Required|IntIn:1,2,3|>>>:" . $this->getTranslation()->_('jobtransfer_0043'),
            "leave_staffs[*].staff_info_id" => "Int",
            "working_day_rest_type" => "Required|Str|>>>:The working_day_rest_type cannot be empty",
        ];
        if (isset($paramIn['hire_type']) && in_array($paramIn['hire_type'], [HrHcModel::HIRE_TYPE_SPECIAL_CONTRACT_WEEKLY_WORKERS, HrHcModel::HIRE_TYPE_SPECIAL_CONTRACT_HOURLY_WORKER])) {
            $validations['hire_times'] = "Required|IntGeLe:1,365";
        }
        if (isset($paramIn['hire_type']) && in_array($paramIn['hire_type'], [HrHcModel::HIRE_TYPE_SPECIAL_CONTRACT_MONTHLY_WORKERS])) {
            $validations['hire_times'] = "Required|IntGeLe:1,12";
        }
        $validations['hire_type'] = "Required|IntIn:1,2,3,4,5,13,".HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT."|>>>:" . $this->getTranslation()->_('jobtransfer_0044');
        $this->validateCheck($paramIn, $validations);

        $agentJobTitle = (new SettingEnvServer)->getSetVal('individual_contractor_job_title', ',');

        //去除网点限制
//        $storeInfo = (new SysStoreServer())->getStoreInfoByid($paramIn['worknode_id']);
//        if (isCountry(['MY']) && $agentJobTitle && in_array($paramIn['job_title_id'], $agentJobTitle) &&
//            !in_array($paramIn['hire_type'], HrStaffInfoModel::$agentTypeTogether) && $storeInfo['category'] == SysStoreServer::CATEGORY_SP
//        ) {
//            throw new ValidationException($this->getTranslation()->_('err_msg_individual_contractor_not_support_this_hire_type'));
//        }

        $nonHeadOfficePosition = (new HcServer($this->lang, $this->timezone))->getNonHeadOfficePosition();

        //增加限制
        if (
            !empty($paramIn['job_title_id'])
            && !empty($paramIn['worknode_id'])
            && in_array($paramIn['job_title_id'], $nonHeadOfficePosition)
            && $paramIn['worknode_id'] == enums::HEAD_OFFICE_ID
        ) {
            //当前职位的工作地点不可选择Head Office
            throw new ValidationException($this->getTranslation()->_('hc_job_title_not_allow_head_office'));
        }

        if (isCountry(['TH','MY']) && $agentJobTitle && !in_array($paramIn['job_title_id'],
                $agentJobTitle) && in_array($paramIn['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
            throw new ValidationException($this->getTranslation()->_('err_msg_individual_contractor_not_support_this_job_title'));
        }

        $server = (new SalaryServer($this->lang, $this->timezone));

        // 泰国 非一线 语言能力
        if(isCountry('TH') && !$server->isFirstLineJob($paramIn['department_id'], $paramIn['job_title_id']) && empty($paramIn['language_ability'])){
            throw new ValidationException('need language ability');
        }
        /**
         * @see HcServer::addHc
         */
        $resultData = (new HcServer($this->lang, $this->timezone))->setLockConf(60,true)->addHcUseLock($paramIn);
        $this->jsonReturn($resultData);
    }


    /**
     * 雇佣类型 静态文件
     */
    public function hireTypesAction()
    {
        $paramIn   = $this->paramIn;
        $job_title = $paramIn['job_title'] ?? 0;
        $store_id = $paramIn['store_id'] ?? '';
        $jd_id = $paramIn['jd_id'] ?? 0;
        $server = Tools::reBuildCountryInstance(new SysServer($this->lang, $this->timezone), [$this->lang, $this->timezone]);
        $this->jsonReturn(self::checkReturn(['data' => $server->getHireTypesList($job_title , $store_id,$jd_id)]));
    }

    /**
     * 修改HC
     * @return  void
     * @throws Exception
     */
    public function updateHcAction()
    {
        try {
            //[1]参数定义
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $paramIn['userinfo'] = $this->userinfo;

            //[2]数据验证
            $validations = [
                "audit_id"     => "Required",
                "status"       => "Required|Int",
                "reject_reason"=> "Required|StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('4014'),
                "new_demand_number" => "IntGe:0",
            ];
            $this->validateCheck($paramIn, $validations);

            //[3]审批操作
            $returnArr = (new HcServer($this->lang, $this->timezone))->updateHcV2($paramIn);

            $this->jsonReturn($returnArr);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('Hc:updateHcAction:'.$e->getMessage() . $e->getTraceAsString() . ":request:" . json_encode($paramIn), 'info');
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 获取部门列表
     */
    public function storeListAction()
    {
        //[1]参数定义
        $paramIn    = $this->paramIn;
        $type       = $this->processingDefault($paramIn, 'type', 2,0);

        //[2]查询部门列表
        $storeData      = (new SysStoreServer())->list($type);

        $this->jsonReturn($this->checkReturn($storeData));
    }

    /**
     * 获取Hc详情
     */
    public function getHcInfoAction()
    {
        try {
            //[1]参数定义
            $paramIn             = $this->paramIn;
            $paramIn['userinfo'] = $this->userinfo;

            //[2]数据验证
            $validations = [
                "hc_id" => "Required|Int",
            ];
            $this->validateCheck($paramIn, $validations);

            //[3]请求数据
            $data = (new HcServer($this->lang, $this->timezone))->getHcInfo($paramIn);

            $this->jsonReturn($this->checkReturn(['data'=>$data]));
        } catch (\Exception $e) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 获取招聘详情
     */
    public function getRecruitDetailAction()
    {
        try {
            //[1]参数定义
            $paramIn = $this->paramIn;

            //[2]数据验证
            $validations = [
                "audit_id" => "Required|Str",
            ];
            $this->validateCheck($paramIn, $validations);

            $hcId = $this->processingDefault($paramIn, 'audit_id');
            $data = (new HcServer($this->lang, $this->timezone))->getRecruitDetail(['hc_id' => $hcId]);

            $this->jsonReturn($this->checkReturn(['data'=>['dataList'=>$data]]));
        } catch (\Exception $e) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 获取个人信息
     */
    public function personInfoAction()
    {
        try {
            //[1]参数定义
            $paramIn = $this->userinfo;

            //[2]获取个人信息
            $data = (new HcServer($this->lang, $this->timezone))->getPersonInfo($paramIn['id']);
            $data = (new SysStoreServer())->formatStoreUseState(array_merge($data,['category' => $data['store_category']]));

            //当前登录人的部门
            $deptId = $data['department_id'];

            //判断是否显示预算人数 申请人部门在 //network operations部门ID //Network Bulky Operations部门
            $is_show_hc = (new HcServer($this->lang, $this->timezone))->isShowHc($deptId);
	        $data = array_merge($data, ['is_show_hc' => $is_show_hc]);

            $this->jsonReturn($this->checkReturn(['data' => $data]));
        } catch (\Exception $e) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 获取优先级
     */
    public function getPriorityListAction()
    {
        $data = (new HcServer($this->lang, $this->timezone))->getPriorityList();

        $this->jsonReturn($this->checkReturn(['data'=>['dataList'=>$data]]));
    }

    /**
     * 通过HC桥获取数据
     * @param array $paramIn
     * @return array
     */
    protected function HcBridge($paramIn)
    {
        $method             = $paramIn['method'];
        $param              = $paramIn['param'] ?? null;
        $logger             = $this->getDI()->get('logger');


        $fle_rpc = (new ApiClient("winhr_rpc",'',$method, $this->lang));
        $fle_rpc->setParams($param);
        $res = $fle_rpc->execute();


        $logger->write_log("svc method {$method} param:" . json_encode($param), 'info');

        return $res['result'] ?? $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
    }


    /**
     * 获取部门列表
     */
    public function getDepartmentListAction()
    {
        try {
            //[1]参数定义
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];

            //[2]请求数据
            $data = (new HcServer($this->lang, $this->timezone))->getDepartmentList($paramIn);

            $this->jsonReturn($this->checkReturn(['data'=>$data]));
        } catch (\Exception $e) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 查询用户权限范围内的部门树
     */
    public function getMyDepartmentTreeAction()
    {
        try {
            $paramIn = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $data = (new HcServer($this->lang, $this->timezone))->getMyDepartmentTree($paramIn);
            $this->jsonReturn($this->checkReturn(['data'=>$data]));
        } catch (\Exception $e) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 获取HC参考数据
     */
    public function getHcReferencesAction()
    {
        try {
            //[1]参数定义
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];

            //[2]数据验证
            $validations = [
                "department_id" => "Required|Int",
                "store_id"      => "Required|Str",
                "job_title_id"  => "Required|Int",
            ];
            $this->validateCheck($paramIn, $validations);
            $add_hour = $this->getDI()['config']['application']['add_hour'];
            $paramIn['budget_month'] = gmdate('Y-m', time() + $add_hour * 3600);

            //[3]请求数据
            $server = $this->class_factory("HcServer",$this->lang, $this->timezone);
            $data = $server->getHcReferences($paramIn);

            $this->jsonReturn($data);
        } catch (\Exception $e) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 获取离职停职待离职员工列表
     */
    public function getStaffLevelListAction() {
        try {

            //[1]参数定义
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];

            //[2]数据验证
            $validations = [
                "department_id" => "Required|Int",
                "store_id"      => "Required|Str",
                "job_title_id"  => "Required|Int",
            ];
            $this->validateCheck($paramIn, $validations);
            
            $list = (new HcServer($this->lang, $this->timezone))->getHcStaffLeaveList($paramIn);
            $this->jsonReturn($this->checkReturn(['data'=>$list]));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log('Hc:getStaffLevelList:'.$e->getMessage() . ":request:" . json_encode($paramIn), 'info');
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 批量创建HC
     */
    public function batchAction()
    {
        //[1]参数定义
        if(!$this->request->hasFiles()){
            exit('no file found');
        }
        $files = $this->request->getUploadedFiles();
        $staffId = $this->userinfo['staff_id'];

        try {
            $list = (new HcServer($this->lang, $this->timezone))->batchInsertHc($files, $staffId);
            $this->jsonReturn($this->checkReturn(['data'=>$list]));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log('Hc:batchAction:'.$e->getMessage() . ":request:" . json_encode($files), 'info');
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 获取 部门职位 关联的 JD
     */
    public function getJdByRelationAction()
    {
        //[1]参数定义
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[2]数据验证
        $validations = [
            "department_id" => "Required|IntGt:0|>>>: department_id  param error",
            "job_title_id"  => "Required|IntGt:0|>>>: job_title_id  param error",
        ];
        $this->validateCheck($paramIn, $validations);

        $data = (new HcServer($this->lang, $this->timezone))->getJdByRelationInfo($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * @return void
     */
    public function getStoreDataAction()
    {
        $deptId = $this->paramIn['department_id'];
        $storeId = $this->paramIn['worknode_id'];
        $jobTitle = $this->paramIn['job_title_id'];
        $validations = [
            "department_id" => "Required|IntGt:0|>>>: department_id param error",
            "store_id" => "Required|Str|>>>: store_id param error",
            "job_title" => "Required|IntGt:0|>>>: job_title param error",
        ];
        $this->validateCheck(['store_id'=> $storeId,'department_id'=>$deptId,'job_title'=>$jobTitle], $validations);
        $data = $this->server->getDisplayStoreData([
            'worknode_id' => $storeId,
            'department_id' => $deptId,
            'job_title' => $jobTitle,
            'approval_state_code' => enums::$audit_status['panding_approval'],
        ]);
        $this->jsonReturn($this->checkReturn(['data'=>$data]));
    }

    /**
     * 导入预算
     */
    public function submitAction()
    {
        //return;
        $excel_file = $this->request->getUploadedFiles();
        if (empty($excel_file)) {
            $this->jsonReturn(self::checkReturn(-3, 'Excel File Empty'));
        }
        $extension = $excel_file[0]->getExtension();
        if ($extension !== "xlsx") {
            $this->jsonReturn(self::checkReturn(-3, 'extension is not \.xlsx'));
        }
        $config = ['path' => ''];
        $excel  = new \Vtiful\Kernel\Excel($config);

        // 读取文件
        $excel_data = $excel->openFile($excel_file[0]->getTempName())
            ->openSheet()
            ->sheetList();

        if (empty($excel_data)){
            $this->jsonReturn(self::checkReturn(-3, 'data is empty!'));
        }
        foreach ($excel_data as $sheetIndex => $sheetName) {
            $sheetData = [];
            if (in_array($sheetIndex, [0, 1])) { //导入数据
                $sheetData = $excel
                    ->openSheet($sheetName)
                    ->setSkipRows(0)
                    ->setType([
                        0 => \Vtiful\Kernel\Excel::TYPE_INT,
                        1 => \Vtiful\Kernel\Excel::TYPE_STRING,
                        2 => \Vtiful\Kernel\Excel::TYPE_INT,
                        3 => \Vtiful\Kernel\Excel::TYPE_STRING,
                        4 => \Vtiful\Kernel\Excel::TYPE_INT,
                    ])
                    ->getSheetData();
                $this->checkData($sheetData);
                continue;
            }

//            if ($sheetIndex == 2) { //标记解除
//                $sheetData = $excel
//                    ->openSheet($sheetName)
//                    ->setSkipRows(0)
//                    ->setType([
//                        0 => \Vtiful\Kernel\Excel::TYPE_INT,
//                        1 => \Vtiful\Kernel\Excel::TYPE_STRING,
//                        2 => \Vtiful\Kernel\Excel::TYPE_INT,
//                        3 => \Vtiful\Kernel\Excel::TYPE_STRING,
//                        4 => \Vtiful\Kernel\Excel::TYPE_INT,
//                    ])
//                    ->getSheetData();
//                $this->markDelete($sheetData);
//            }
        }
    }

    /**
     * @param $data
     * @return void
     */
    private function checkData($data)
    {
        $departmentIds = array_values(array_filter(array_column($data, 0)));
        $jobTitleIds   = array_values(array_filter(array_column($data, 2)));

        if (empty($departmentIds) || empty($jobTitleIds)) {
            return;
        }

        //部门是否有效
        $departmentInfo = SysDepartmentModel::find([
            'columns' => 'id',
            'conditions' => 'id in({ids:array}) and deleted = 0',
            'bind' => [
                'ids' => $departmentIds
            ]
        ])->toArray();
        $departmentValidIds = array_column($departmentInfo, 'id');

        //职位是否有效
        $jobTitleInfo = HrJobTitleModel::find([
            'columns' => 'id',
            'conditions' => 'id in({ids:array}) and status = 1',
            'bind' => [
                'ids' => $jobTitleIds
            ]
        ])->toArray();
        $jobTitleValidIds = array_column($jobTitleInfo, 'id');

        foreach ($data as $item) {
            $departmentId = $item[0];
            $jobTitleId   = $item[2];
            $budgetCount  = $item[4];

            $info = HrJobDepartmentRelationModel::findFirst([
                'conditions' => 'department_id = :department_id: and job_id = :job_id:',
                'bind' => [
                    'department_id' => $departmentId,
                    'job_id'        => $jobTitleId,
                ]
            ]);
            if (empty($info)) {
                echo sprintf('department id:%d,job Title id:%d , bind relation not exist', $departmentId, $jobTitleId), PHP_EOL;
                continue;
            }

            if (!in_array($departmentId, $departmentValidIds)) {
                //$info->error_code = 1;
                //$info->update();
                echo sprintf('department id:%d,job Title id:%d , department not exist', $departmentId, $jobTitleId), PHP_EOL;
                continue;
            }

            if (!in_array($jobTitleId, $jobTitleValidIds)) {
                //$info->error_code = 2;
                //$info->update();
                echo sprintf('department id:%d,job Title id:%d , job title not exist', $departmentId, $jobTitleId), PHP_EOL;
                continue;
            }
            $info->plan_hc_nums = $budgetCount;
            $info->update();
            echo sprintf('Update success!department id:%d,job Title id:%d ,budget num:%d', $departmentId, $jobTitleId, $budgetCount), PHP_EOL;
        }
    }

    private function markDelete($data)
    {
        foreach ($data as $item) {
            $departmentId = $item[0];
            $jobTitleId   = $item[2];

            $info = HrJobDepartmentRelationModel::findFirst([
                'conditions' => 'department_id = :department_id: and job_id = :job_id:',
                'bind' => [
                    'department_id' => $departmentId,
                    'job_id'        => $jobTitleId,
                ]
            ]);
            if (empty($info)) {
                echo sprintf('department id:%d,job Title id:%d , bind relation not exist', $departmentId, $jobTitleId), PHP_EOL;
                continue;
            }
            $info->delete();
            echo sprintf('Delete success!department id:%d,job Title id:%d', $departmentId, $jobTitleId), PHP_EOL;
        }
    }
}
