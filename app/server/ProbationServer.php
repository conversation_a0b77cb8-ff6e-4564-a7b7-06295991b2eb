<?php


namespace FlashExpress\bi\App\Server;

use App\Country\Tools;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrProbationAttendanceContractWorkerModel;
use FlashExpress\bi\App\Models\backyard\HrProbationAttendanceModel;
use FlashExpress\bi\App\Models\backyard\HrProbationAuditModel;
use FlashExpress\bi\App\Models\backyard\HrProbationAuditContractWorkerModel;
use FlashExpress\bi\App\Models\backyard\HrProbationContractWorkerModel;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\HrProbationResignModel;
use FlashExpress\bi\App\Models\backyard\HrProbationTargetDetailModel;
use FlashExpress\bi\App\Models\backyard\HrProbationTargetModel;
use FlashExpress\bi\App\Models\backyard\HrProbationTplItemModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\MessagePdfModel;
use FlashExpress\bi\App\Models\backyard\MessageWarningModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\ViolationReasonTypeModel;
use FlashExpress\bi\App\Repository\StaffRepository;


class ProbationServer extends BaseServer
{
    /**
     * 合格分数
     * @var int
     */
    public $passMark = 6000;
    //小于等于 17 级的配置项
    public $evaluate_day = [
        '1' => 40,     //小于等于 17 的第一阶段发送时间
        '2' => 75,      //小于等于 17 的第二阶段发送时间
    ];
    public $formal_days = 120;  //转正时间
    public $duration_day = [
        //第一阶段
        '1' => [
            '1' => 3, //第一次  上级 评估天数
            '2' => 3, //第二次  上级上级 评估天数
        ],
        //第二阶段
        '2' => [
            '1' => 6, //第一次  上级 评估天数
            '2' => 5, //第二次  上级上级 评估天数

        ],
    ];
    // 检查是否第一二阶段终止 日期
    public $first_check_days = 90; // 第一阶段 90天的时候检查
    public $second_check_days = 145; // 第二阶段 145天的时候检查

    public $job_grade_exceed = 17; //职级分界线
    //大于 17 职级的
    public $first_check_days_exceed = 95; // 第一阶段 95天的时候检查
    public $second_check_days_exceed = 150; // 第二阶段 150天的时候检查
    public $cpo_staff_id = 56780; // cop 工号
    public $evaluate_day_exceed =[
        '1' => 40, //大于 17 的第一阶段发送时间
        '2' => 75, //大于 17 的第一阶段发送时间
    ];
    public $formal_days_exceed = 120; //转正日期
    public $duration_day_exceed = [
        //第一阶段
        '1' => [
            '1' => 3, //第一次  上级 评估天数
            '2' => 3, //第二次  上级上级 评估天数
            '3' => 3, //第二次  上级上级 评估天数
        ],
        //第二阶段
        '2' => [
            '1' => 6, //第一次  上级  评估天数
            '2' => 5, //第二次  上级上级 评估天数
            '3' => 3, //第三次    cop 评估天数
        ],
    ];
    //version
    public $version = 14310; // 14310 版本增加 18 级 3 级 cpo 审批

	public const STATUS_PROBATION = 1; //试用期
	public const STATUS_PASS      = 2; //已通过
	public const STATUS_NOT_PASS  = 3; //未通过
	public const STATUS_FORMAL    = 4; //已转正
	
	public const AUDIT_STATUS_PENDING = 1;    //审核状态，待处理
	public const AUDIT_STATUS_DEAL    = 2;    //审核状态，已处理
	public const AUDIT_STATUS_TIMEOUT = 3;    //审核状态，已超时

	public const CUR_LEVEL_FIRST  = 1;  //第一次评审
	public const CUR_LEVEL_SECOND = 2;  //第二次评审

    public const  AUDIT_LEVEL_1 = 1; //第一级评审 上级
    public const  AUDIT_LEVEL_2 = 2; //第二级评审 上上级
    public const  AUDIT_LEVEL_3 = 3; //第三级评审 cpo

    public const TERM_YES = 1;//终止试用

    public const TERM_NO = 2;//继续留用


    public $add_hour = '7';  //误差时间

    public function __construct( $lang = 'zh-CN', $timezone ) {
        parent::__construct($lang);
        $this->add_hour = $this->getDI()['config']['application']['add_hour'];
        //获取配置项
        $probation_evaluate_env         = $this->getProbationEvaluateEnv();
        $this->evaluate_day             = $probation_evaluate_env['evaluate_day'] ?? $this->evaluate_day;
        $this->formal_days              = $probation_evaluate_env['formal_days'] ?? $this->formal_days;
        $this->duration_day              = $probation_evaluate_env['duration_day'] ?? $this->duration_day;
        $this->first_check_days         = $probation_evaluate_env['first_check_days'] ?? $this->first_check_days;
        $this->second_check_days        = $probation_evaluate_env['second_check_days'] ?? $this->second_check_days;
        $this->job_grade_exceed         = $probation_evaluate_env['job_grade_exceed'] ?? $this->job_grade_exceed;
        $this->first_check_days_exceed  = $probation_evaluate_env['first_check_days_exceed'] ?? $this->first_check_days_exceed;
        $this->second_check_days_exceed = $probation_evaluate_env['second_check_days_exceed'] ?? $this->second_check_days_exceed;
        $this->cpo_staff_id             = $probation_evaluate_env['cpo_staff_id'] ?? $this->cpo_staff_id;
        $this->evaluate_day_exceed      = $probation_evaluate_env['evaluate_day_exceed'] ?? $this->evaluate_day_exceed;
        $this->formal_days_exceed       = $probation_evaluate_env['formal_days_exceed'] ?? $this->formal_days_exceed;
        $this->duration_day_exceed      = $probation_evaluate_env['duration_day_exceed'] ?? $this->duration_day_exceed;
    }
	/**
	 * Backyard转正评估
	 *
	 * @param $params [
	 *                "audit_id"=> int                              用户 id
	 *                "is_deal" => "Required|IntIn:1,2",             1 待处理  2 已处理 3 已超时
	 *                'page_num'=>"IntGe:1",                            页数
	 *                'page_size'=>"IntGe:1",]                            每页数量
	 *
	 * @return array ["dataList" => $arr, "pagination" => ["pageNum" => "" . $page_num, "pageSize" => "" . $page_size,
	 *                     "count" => $count]];
	 */
	public function getByList($params)
	{
        $page_num = $params['page_num'] ?? 1;
        $page_size = $params['page_size'] ?? 20;
        if (empty($params['audit_id']) || empty($params['is_deal'])) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }

        $conditions = " hpa.audit_id = :audit_id: and hp.is_system=0 ";
        $bina['audit_id'] = $params['audit_id'];
        //$order = "hpa.id asc";
        $order = "hpa.created_at DESC";
        if ($params['is_deal'] == self::AUDIT_STATUS_PENDING) { //待处理
            $conditions .= " and hpa.audit_status = :audit_status: and hp.status != :hp_status:";
            $bina['audit_status'] = self::AUDIT_STATUS_PENDING;
            $bina['hp_status'] = self::STATUS_FORMAL;
        } else if($params['is_deal'] == self::AUDIT_STATUS_TIMEOUT){ // 已过期  过期的数据不查询激活的
            $conditions .= " and hpa.audit_status = :audit_status:  and hpa.is_active = 0 and hp.status != :hp_status:";
            $bina['audit_status'] = self::AUDIT_STATUS_TIMEOUT;
            $bina['hp_status'] = self::STATUS_FORMAL;
            //$order = 'hsi.state asc ,hpa.deadline_at desc';
        } else {  //已处理
            $conditions .= " and hpa.audit_status = :audit_status: ";
            $bina['audit_status'] = self::AUDIT_STATUS_DEAL;
        }
        //阶段
        if(!empty($params['cur_level'])){
            $conditions .= " and hpa.cur_level  = :cur_level: ";
            $bina['cur_level'] = (int)$params['cur_level'];
        }

        // 构建第一个查询 - hr_probation_audit 表
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $builder->from(['hpa' => HrProbationAuditModel::class]);
        $builder->join(HrStaffInfoModel::class, 'hsi.staff_info_id = hpa.staff_info_id', 'hsi');
        $builder->join(HrProbationModel::class, 'hp.id = hpa.probation_id', 'hp');
        $builder->where($conditions, $bina);

        // 构建第二个查询 - hr_probation_audit_contract_worker 表
        $builderContract = $this->modelsManager->createBuilder();
        $builderContract->columns('count(1) as total');
        $builderContract->from(['hpa' => HrProbationAuditContractWorkerModel::class]);
        $builderContract->join(HrStaffInfoModel::class, 'hsi.staff_info_id = hpa.staff_info_id', 'hsi');
        $builderContract->join(HrProbationContractWorkerModel::class, 'hp.id = hpa.probation_id', 'hp');
        $builderContract->where($conditions, $bina);

        // 获取两个表的总数量
        $totalInfo = $builder->getQuery()->getSingleResult();
        $totalContractInfo = $builderContract->getQuery()->getSingleResult();
        $haveContractData = !empty($totalContractInfo->total);
        $count = (int)($totalInfo->total) + (int)($totalContractInfo->total);

        //获取列表数据
        $columns = ' hpa.id,
                hsi.staff_info_id,
                hsi.name,
                hsi.name_en,
                hsi.hire_date,
                hsi.job_title,
                hpa.audit_status,
                hpa.status,
                hpa.created_at,
                hpa.updated_at,
                hpa.deadline_at,
                hpa.cur_level,
                hpa.audit_level,
                hpa.probation_id,
                hpa.version,
                hsi.job_title_grade_v2,
                hp.status as hp_status,
                hp.is_active as hp_active,
                hp.hire_type,
                hp.formal_at,
                hp.first_audit_status,
                hp.contract_formal_date,
                hp.probation_channel_type
                ';

        // 构建第一个查询的完整SQL
        $builder->columns($columns);
        $sql1 = $builder->getQuery()->getSql();

        if ($haveContractData) {
            // 构建第二个查询的完整SQL
            $builderContract->columns($columns);
            $sql2 = $builderContract->getQuery()->getSql();
            // 合并两个查询
            $unionSql = "({$sql1['sql']}) UNION ALL ({$sql2['sql']}) ORDER BY created_at DESC LIMIT " . ($page_num-1)*$page_size . ", " . $page_size;
            // 合并绑定参数
            $bindParams = array_merge($sql1['bind'], $sql2['bind']);
        } else {
            $unionSql = "({$sql1['sql']}) ORDER BY created_at DESC LIMIT " . ($page_num-1)*$page_size . ", " . $page_size;
            $bindParams = $sql1['bind'];
        }

        // 执行合并后的查询
        $arr = $this->getDI()->get('db_rby')->fetchAll($unionSql, \PDO::FETCH_ASSOC, $bindParams);
        //查询 job_title
        $job_title_ids = array_values(array_unique(array_column($arr, 'job_title')));
        $job_title_arr = [];
        if($job_title_ids){
            $job_list        = HrJobTitleModel::find([
                'columns'    => ['job_name', 'id'],
                'conditions' => 'id in ({job_title_ids:array})',
                'bind'       => ['job_title_ids' => $job_title_ids],
            ])->toArray();
            $job_title_arr =  array_column($job_list, 'job_name','id');
        }

        $now = date("Y-m-d H:i:s");
        foreach ($arr as $k => $v) {
            $arr[$k]['job_name'] =  $job_title_arr[$v['job_title']] ?? '';
            $arr[$k]['is_timeout'] = 0; // 1 是已超时

            if ($v['cur_level'] == self::CUR_LEVEL_FIRST) {  // 1 上级 2 上上级
                //待评估才有超时的概念
                if ($v['audit_status'] == self::AUDIT_STATUS_PENDING) {  //
                    $deadline_at = $v['deadline_at'];
                    $end   = $deadline_at;
                    if ($v['audit_level'] == self::AUDIT_LEVEL_1) { //第一次
                        $start = $this->getDateByDays($deadline_at, 1); //1 天
                    } else {  //第二次
                        $start = $this->getDateByDays($deadline_at, 2);//2 天
                    }
                    if ($now >= $start && $now < $end) { //是否超时
                        $arr[$k]['is_timeout'] = 1;
                    }
                }
                $arr[$k]['audit_status_text'] = $this->getAuditStatusText($v['audit_status']); // 审核状态
            } else {
                $arr[$k]['audit_status_text'] = $this->getSecondStatusText($v['status'], $v['audit_status']); //状态
            }

            $arr[$k]['name_en'] = $v['name_en'] ?? '';
            if (!empty($arr[$k]['hire_date'])) { //入职日期
                $arr[$k]['hire_date'] = date("Y-m-d", strtotime($v['hire_date']));
            }
            //是否展示激活
            $arr[$k]['is_can_active'] = $this->isCanActive($v) ? 1 : 0;
            if (isCountry('PH')) {
                //雇佣类型
                $arr[$k]['hire_type_text'] = empty($v['hire_type']) ? '' :  $this->getTranslation()->_('hire_type_'.$v['hire_type']);
            } else {
                unset($arr[$k]['hire_type']);
            }

        }
        $returnData['data'] = [
            "dataList"   => $arr,
            "pagination" => [
                "pageNum"  => (int)$page_num,
                "pageSize" => (int)$page_size,
                "count"    => $count,
            ],
        ];
        return $this->checkReturn($returnData);


    }
	
	/**
	 * @description:详情
	 *
	 * @param string $id
	 * @param string $audit_id
	 *
	 * @return array :
	 * <AUTHOR> L.J
	 * @time       : 2021/9/8 20:49
	 */
	public function getByDetail($paramIn = [])
	{
		
		try {
			$id       = $paramIn['id'];
			$audit_id = $paramIn['audit_id'] ?? 0;
			$staff_info_id = $paramIn['staff_id'] ?? 0;
			
			if (!isset($paramIn['type']) && (empty($id) || empty($audit_id))) {
					throw new \Exception($this->getTranslation()->_('miss_args'));
			}
			
			
			$sql = "
            select
                hpa.*,
                it.value as avatar,
                hp.status as hp_status,
                hp.is_active as hp_active,
                hp.first_score,
                hp.probation_channel_type,
                hp.first_audit_status,
                hp.second_audit_status
            from
                hr_probation_audit as hpa
            left join hr_staff_items as it on it.staff_info_id=hpa.staff_info_id and it.item='PROFILE_OBJECT_KEY'
            left join hr_probation as hp on hp.id=hpa.probation_id
            where  hpa.id=:id ";
		$where_data['id'] =	$id;
		if(!empty($audit_id)){
			$sql .= 'and hpa.audit_id=:audit_id';
			$where_data['audit_id'] =	$audit_id;
		}
		if(!empty($staff_info_id)){
			$sql .= 'and hpa.staff_info_id=:staff_info_id';
			$where_data['staff_info_id'] =	$staff_info_id;
		}
		
			$arr = $this->getDI()->get("db_rby")->fetchOne($sql, \PDO::FETCH_ASSOC, $where_data);
			if (empty($arr)) {
				throw new \Exception($this->getTranslation()->_('4008'));
			}
			//获取员工信息
            $staffInfo = HrStaffInfoModel::findFirst([
                                                         'conditions' => 'staff_info_id = :staff_info_id:',
                                                         'bind'       => [
                                                             'staff_info_id' => $arr['staff_info_id'] ?? '',
                                                         ],
                                                         'columns' => 'name,staff_info_id,hire_date,sys_store_id,job_title,sys_department_id,job_title_grade_v2,contract_company_id',
                                                     ]);
            $staffInfo = !empty($staffInfo) ? $staffInfo->toArray() :[];
            $arr = array_merge($arr,$staffInfo);
            $job_title = HrJobTitleModel::findFirst(['conditions' => 'id = :id: ',
                                                    'bind'       => ['id' => $staffInfo['job_title'] ?? ''],
                                                    'columns'    => 'id,job_name',
                                                   ]);
            $arr['job_name'] = !empty($job_title) ? $job_title->job_name : '';
            $department_name = SysDepartmentModel::findFirst(['conditions' => 'id = :id: ',
                                                              'bind'       => ['id' =>  $staffInfo['sys_department_id'] ?? ''],
                                                              'columns'    => 'id,name',
                                                             ]);
            $arr['department_name'] = !empty($department_name) ? $department_name->name : '';

			if (!empty($arr['hire_date'])) {
				$arr['hire_date'] = date("Y-m-d", strtotime($arr['hire_date']));
			}
			
			if (!empty($arr['avatar'])) {
				$arr['avatar'] = $this->getDI()['config']['application']['img_prefix'] . $arr['avatar'];
			} else {
				$arr['avatar'] = "";
			}

			$arr['rules'] = [];
			
			$rule_num = 5;
			if ($arr['cur_level'] == self::CUR_LEVEL_SECOND) {
				$rule_num = 6;
			}
			for ($i = 1; $i <= $rule_num; $i++) {
				$arr['rules'][] = $this->getTranslation()->_("hr_probation_rule_" . $i);
			}
			
			$tpl = $this->getTplItem($arr['tpl_id'], $arr['score']);
			if (empty($tpl['code'])) {
				throw new \Exception(json_encode($tpl));
			}
            $tpl['score']['score'] = empty($tpl['score']['score']) ? $this->getScore($arr['first_score']) : $tpl['score']['score'];
			
			//计算规则
			$arr['score_rule_text'] = $tpl['score_rule_text'];
			$arr['score']           = $tpl['score'];
			
			$logs = $this->getProbationLogs($arr['probation_id'],$arr['probation_channel_type'],$arr);
			if (empty($logs['code'])) {
				throw new \Exception(json_encode($logs));
			}
			$arr['audit_logs'] = $logs['data'];
			
			if ($arr['sys_store_id'] == -1) {
				$arr['sys_store_id'] = enums::HEAD_OFFICE;
			}
			
			$arr['is_can_active'] = $this->isCanActive($arr);
			
			$returnData['data'] = $arr;
			return $this->checkReturn($returnData);
			
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->write_log('ProbationServer_getByList---' . $e->getMessage() . '-----' . $e->getLine());
			$returnData = ['msg' => $e->getMessage(),'code'=>ErrCode::ERROR];
			return $this->checkReturn($returnData);
		}
	}
	
	
	/**
	 * 根据模板id，获得模板内容
	 *
	 * @param $tpl_id
	 *
	 * @return string | array
	 */
	
	
	public function getTplItem($tpl_id = '', $score = '')
	{
		try {
			if (empty($tpl_id)) {
				throw new \Exception($this->getTranslation()->_('miss_args'));
			}
			$lang = $this->getTranslation();
			$sql  = "
            select
                *
            from
                hr_probation_tpl
            where
                id=:id
        ";
			
			$arr = $this->getDI()->get("db_rby")->fetchOne($sql, \PDO::FETCH_ASSOC, ["id" => $tpl_id]);
			
			$itemIdArr    = explode(",", $arr['item_ids']);
			$itemScoreArr = explode(",", $arr['score_rule']);
			if (count($itemIdArr) == 0 || count($itemIdArr) != count($itemScoreArr)) {
				throw new \Exception('tpl error');
			}
			
			$items = HrProbationTplItemModel::find([
				                                       'conditions' => ' id in ({ids:array}) or pid in ({ids:array}) ',
				                                       'bind'       => [
					                                       'ids' => $itemIdArr,
				                                       ],
			                                       ])->toArray();
			if (empty($items)) {
				throw new \Exception('not found tpl items');
			}
			$itemArr = array_column($items, null, "id");
			
			
			$score_rule_text = "(";
			foreach ($itemIdArr as $k => $v) {
				if ($k != 0) {
					$score_rule_text .= "+";
				}
				$score_rule_text .= $lang->_($itemArr[$v]['name']) . "*" . (($itemScoreArr[$k]) / 100);
			}
			$score_rule_text .= ")";
			
			if (!empty($score)) {
				$score = json_decode($score, 1);
				//对应的名字，info根据语言环境变化。如果更改的话，不变。
				foreach ($score['list'] as $k => $v) {
					$subscript = $k+1;
					$score['list'][$k]['subscript'] = $score['list'][$k]['subscript'] ?? $subscript;
					$score['list'][$k]['name'] = $lang->_($v['name_key']);
					$score['list'][$k]['info'] = $lang->_($v['info_key']);
					$score['list'][$k]['state'] =  (string) ($score['list'][$k]['state'] ?? 1);// 1问题 2 是纯展示
					$i = 0;
					foreach ($v['list'] as $kk => $vv) {
						$score['list'][$k]['list'][$kk]['name'] = $lang->_($vv['name_key']);
						$score['list'][$k]['list'][$kk]['state'] =  (string) ( $score['list'][$k]['list'][$kk]['state'] ?? 1);// 1问题 2 是纯展示
						if($score['list'][$k]['list'][$kk]['state'] == 1){
							$i++;
						}
						$score['list'][$k]['list'][$kk]['subscript'] = $score['list'][$k]['list'][$kk]['subscript'] ?? $score['list'][$k]['subscript'].'.'.$i;
					
						
						if (empty($vv['is_update'])) {
							$score['list'][$k]['list'][$kk]['info'] = $lang->_($vv['info_key']);
						}
					}
				}
			} else {
				//$score 赋值数组
				$score=[];
				$score['score']        = "";
				$score['second_score'] = "";
				$score['list']         = [];
				
				foreach ($itemIdArr as $k => $v) {
					$tmp             = [];
					$tmp['subscript'] = $k+1;
					$tmp['id']       = $itemArr[$v]['id'];
					$tmp['name_key'] = $itemArr[$v]['name'];
					$tmp['name']     = $lang->_($itemArr[$v]['name']);
					
					$tmp['info_key'] = $itemArr[$v]['info'];//个人素质总得分
					$tmp['info']     = $lang->_($itemArr[$v]['info']);
					
					$tmp['score']        = "";
					$tmp['second_score'] = "";
					$tmp['score_rule']   = $itemScoreArr[$k];
					$tmp['state'] =  (string) ($itemArr[$v]['state'] ?? 1);  // 1问题 2 是纯展示
					$tmp['list']         = [];
					$tmps[$tmp['id']]['subscript'] = 0;
					
					foreach ($items as $kk => $vv) {
						if ($vv['pid'] == $tmp['id']) {
							$tmp2                 = [];
							$tmp2['id']           = $vv['id'];
							$tmp2['name_key']     = $vv['name'];
							$tmp2['name']         = $lang->_($vv['name']);
							$tmp2['score']        = "";
							$tmp2['second_score'] = "";
							$tmp2['score_rule']   = $vv['score_rule'];
							
							$tmp2['info_key']  = $vv['info'];
							$tmp2['info']      = $lang->_($vv['info']);
							$tmp2['is_update'] = 0;
							$tmp2['state'] = (string) ($vv['state'] ?? 1);
							if($tmp2['state'] == 1){
								$tmps[$tmp['id']]['subscript'] = $tmps[$tmp['id']]['subscript']+1;
								$tmp2['subscript'] = $tmp['subscript'].'.'.$tmps[$tmp['id']]['subscript'];
							}
							
							$tmp['list'][]     = $tmp2;
						}
					}
					$score['list'][] = $tmp;
				}
			}
			return ['code' => 1, "score_rule_text" => $score_rule_text, "score" => $score, 'question_num' => count($itemArr)];
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->write_log('ProbationServer_getTplItem---' . $e->getMessage() . '-----' . $e->getLine());
			$returnData = ['msg' => $e->getMessage()];
			return $this->checkReturn($returnData);
		}
	}

    /**
     * bi获得转正评估待审批的数量,因为要频繁调用，就直接查数据库了，不走svc
     * bi = svc probation_num 还有段类似代码，是h5里面用的
     */
    public function getProbationNum($staff_info_id)
    {
        if (empty($staff_info_id)) {
            return 0;
        }
        $sql1 = "select count(*) as num from hr_probation_audit as hpa left join hr_probation as hp on hp.id=hpa.probation_id where hpa.audit_id= :audit_id and hpa.audit_status = 1 and hp.is_system=0 and hp.status!=4 ";
        $return = $this->getDI()->get("db_rby")->fetchColumn($sql1, ["audit_id" => $staff_info_id]);
        if (isCountry('PH')) {
            $sql2 = "select count(*) as num from hr_probation_audit_contract_worker as hpa left join hr_probation_contract_worker as hp on hp.id=hpa.probation_id where hpa.audit_id= :audit_id and hpa.audit_status = 1 and hp.is_system=0 and hp.status!=4 ";
            $returnContract = $this->getDI()->get("db_rby")->fetchColumn($sql2, ["audit_id" => $staff_info_id]);
            return $return + $returnContract;
        }
        return $return;
    }

	/**
	 * 获得审批人，待审批人的数量，是否显示菜单
	 *
	 * @param $staff_info_id
	 *
	 * @return mixed
	 */
	public function getAuditNum($staff_info_id = 0)
	{
		try {
			if (empty($staff_info_id)) {
				throw new \Exception($this->getTranslation()->_('miss_args'));
			}
			$arr        = [];
			$arr['num'] = $this->getProbationNum($staff_info_id);
			$sql        = "select * from hr_probation_audit where audit_id= :audit_id limit 1";
			$temp       = $this->getDI()->get("db_rby")->fetchOne($sql, \PDO::FETCH_ASSOC, ["audit_id" => $staff_info_id]);
            if (isCountry('PH')) {
                $tempContract   = $this->getDI()->get("db_rby")->fetchOne("select * from hr_probation_audit_contract_worker where audit_id= :audit_id limit 1",
                    \PDO::FETCH_ASSOC, ["audit_id" => $staff_info_id]);
                $arr['is_show'] = (empty($temp) && empty($tempContract)) ? '0' : '1';
            } else {
                $arr['is_show'] = empty($temp) ? '0' : '1';
            }
			$returnData['data'] = $arr;
			return $this->checkReturn($returnData);
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->write_log('ProbationServer_getAuditNum---' . $e->getMessage() . '-----' . $e->getLine());
			$returnData = ['code' => 0, 'msg' => $e->getMessage(), 'data' => ['is_show' => 0, 'num' => 0]];
			return $this->checkReturn($returnData);
		}
	}
	
	//by 试用期，评审

    /**
     * @description:提交转正评估评审
     *
     * @param string $id
     * @param string $audit_id 用户 id
     * @param array $params
     * @param int $deadline_at_one 第一阶段上上级过期时间
     * @param int $is_fail_msg 未通过是否发送消息给被评估员工上级，以及被评估员工所属HRBP 目前只有 Id 发送  2 是发送
     * @return     :
     * @throws ValidationException
     * <AUTHOR> L.J
     * @time       : 2021/9/9 14:41
     */
    public function probation_audit( $id = '', $audit_id = '', $params = [], $deadline_at_one = 3, $is_fail_msg = 1 ) {

        if (empty($id) || empty($audit_id)) {
            throw new \Exception($this->getTranslation()->_('miss_args'));
        }
        $lang = $this->getTranslation();
        //没有分数不行!!
        if(empty($params['score'])){
            throw new ValidationException('score '. $lang->_('miss_args'));
        }


        $sql = "
            select
                hpa.*,
                hpt.item_ids,
                hpt.score_rule,
                hp.is_system,
                hp.formal_at,
                hp.status as hp_status,
                hp.is_active as hp_is_active
            from
                hr_probation_audit as hpa
                left join hr_probation_tpl as hpt on hpa.tpl_id = hpt.id
                left join hr_probation as hp on hp.id=hpa.probation_id
            where
                hpa.id=:id and hpa.audit_id=:audit_id
        ";

        $arr = $this->getDI()->get("db_rby")->fetchOne($sql, \PDO::FETCH_ASSOC, ["id" => $id, "audit_id" => $audit_id]);
        if (empty($arr)) {
            throw new ValidationException('not found');
        }
        if ($arr['hp_status'] == HrProbationModel::STATUS_FORMAL) {
            throw new ValidationException($lang->_('probation_status_err_1'));
        }

        if ($arr['audit_status'] != 1) {
            throw new ValidationException('has already audit');
        }

        if ($arr['is_system'] == 1) {
            throw new ValidationException('system auto');
        }

        //下个审批人
        $manager_id = $this->getManagerId($audit_id);
        //查询用户等级
        $staffInfo = HrStaffInfoModel::findFirst([
                                                     'conditions' => 'staff_info_id = :staff_info_id:',
                                                     'bind'       => [
                                                         'staff_info_id' => $arr['staff_info_id'] ?? '',
                                                     ],
                                                 ]);
        $staffInfo = !empty($staffInfo) ? $staffInfo->toArray() : [];

        $is_send = false;                    //是否继续发送下一级评估
        //获取增加天数
        $evaluate_time = $this->duration_day;//递增天数
        //18 级以下  发两次
        $is_send = $arr['audit_level'] == self::AUDIT_LEVEL_1 && !empty($manager_id);
        //18 级以上 发三次  第三次是 cpo
        if (isset($staffInfo['job_title_grade_v2']) && $staffInfo['job_title_grade_v2'] > $this->job_grade_exceed &&
            $arr['version'] == $this->version) {
            $cpo_staff_info_id = $this->cpo_staff_id;
            //如果当前审批人 == cop 或者 当前阶段是第三级评估  则不发送了   false 为不发送
            $is_send = !($audit_id == $cpo_staff_info_id || $arr['audit_level'] >= self::AUDIT_LEVEL_3);
            //如果当前是第二级评估   则 下次评估人为 cpo 评估
            $manager_id    = $arr['audit_level'] >= self::AUDIT_LEVEL_2  ? $cpo_staff_info_id : $manager_id;
            $evaluate_time = $this->duration_day_exceed;//递增的截止天数

        }


        $score       = $params['score'];
        //第二阶段没选择是否让继续工作了，通过分数判断下
        if ($arr['cur_level'] == 2 && $this->getScore($score['second_score'], 1) < $this->passMark) {
            $params['is_terminate'] = HrProbationAuditModel::IS_TERMINATE;
        }
        if ($arr['cur_level'] == 2 && $this->getScore($score['second_score'], 1) >= $this->passMark) {
            $params['is_terminate'] = HrProbationAuditModel::IS_NO_TERMINATE;
        }
        $remark      = isset($params['remark']) ? $params['remark'] : '';
        $pic         = isset($params['pic']) ? $params['pic'] : '';
        $job_content = $params['job_content'] ?? ''; //工作内容

        $item = []; //hr_probation表
        $data = []; //hr_probation_audit表

        //如果info与库里不同,证明已经修改
        foreach ($score['list'] as $k => $v) {
            foreach ($v['list'] as $kk => $vv) {
                if ($lang->_($vv['info_key']) != $vv['info']) {
                    $score['list'][$k]['list'][$kk]['is_update'] = 1;
                }
            }
        }

        //自己根据规则算一遍
        $score = $this->getScoreFromTpl($score, $arr['cur_level']);
        //没有分数不行!!
        if(empty($score)){
            throw new \Exception('score 没有获取到!!!');
        }
        $data['score']        = json_encode($score, JSON_UNESCAPED_UNICODE);//score存json
        $data['audit_status'] = 2;
        $data['remark']       = $remark;
        $data['pic']          = $pic;


        $item['updated_at']  = gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600);
        $data['updated_at']  = $item['updated_at'];
        $data['job_content'] = $job_content;
        //第一阶段
        if ($arr['cur_level'] == self::CUR_LEVEL_FIRST) {
            $score_num           = $this->getScore($score['score'], 1);
            $item['first_score'] = $score_num;
            $item['first_audit_status'] = HrProbationModel::FIRST_AUDIT_STATUS_RUN;
            if (!$is_send) {
                //不给下一级发了就说明是最后一级了
                $item['first_audit_status'] = HrProbationModel::FIRST_AUDIT_STATUS_DONE;
                if ($score_num >= $this->passMark) {
                    $item['first_status'] = HrProbationModel::FIRST_STATUS_PASS;
                }
            }
        } //第二阶段
        else {
            $score_num            = $this->getScore($score['second_score'], 1);
            $item['second_score'] = $score_num;
            //第二阶段上上级评过+第二阶段上级改成上级和上上级都更改主表
            if ($score_num >= $this->passMark) {
                $item['status'] = self::STATUS_PASS;    //已通过
                if ($item['updated_at'] > $arr['formal_at'] ) {
                    $is_send = false;
                    $item['status']          = self::STATUS_FORMAL;
                    $item['formal_staff_id'] = $audit_id;
                    $item['formal_at']       = gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600);
                }
            } else {
                $item['status'] = self::STATUS_NOT_PASS;    //未通过
            }
            $item['remark'] = $data['remark'];

            //把通过不通过的状态传过去
            $data['status'] = $item['status'];

            //不发了 赋值终态
            if (!($is_send && !empty($manager_id))) {
                $item['second_audit_status'] = HrProbationModel::SECOND_AUDIT_STATUS_DONE;
                 if (in_array($item['status'],[self::STATUS_PASS,self::STATUS_FORMAL])) {
                    $item['second_status'] = HrProbationModel::SECOND_STATUS_PASS;
                }
            }
        }

        $db = $this->getDI()->get("db");

        try {
            $db->begin();

            $db->updateAsDict("hr_probation_audit", $data, ["conditions" => "id=" . intval($id)]);
            $db->updateAsDict("hr_probation", $item, ["conditions" => 'id=' . intval($arr['probation_id'])]);

            //如果可以评估 并且有下一级评估人 并且没有转正
            if ($is_send && !empty($manager_id)) {

                //下一级评审，内容大部分相同
                $tmp                  = [];
                $tmp['probation_id']  = $arr['probation_id'];
                $tmp['staff_info_id'] = $arr['staff_info_id'];
                $tmp['tpl_id']        = $arr['tpl_id'];
                $tmp['audit_id']      = $manager_id;
                $tmp['audit_level']   = ((int)$arr['audit_level']) + 1;
                $tmp['cur_level']     = $arr['cur_level'];
                $tmp['audit_status']  = 1;
                $tmp['status']        = 0;
                $tmp['score']         = $data['score'];
                $tmp['created_at']    = $item['updated_at'];
                $tmp['updated_at']    = $item['updated_at'];
                $tmp['remark']        = $remark;              //同步上级评审意见
                $tmp['pic']           = $pic;                 //同步上级图片
                $tmp['job_content']   = $job_content;         //工作内容
                $tmp['version']       = $arr['version'];         //版本
                if ($arr['hp_is_active'] == 1){
                    $now_date = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
                    $tmp['deadline_at']        = $this->getDateByDays($now_date, 3, 1);
                }else{
                    $day                = $evaluate_time[$arr['cur_level']][$tmp['audit_level']] ?? $deadline_at_one;
                    $tmp['deadline_at'] = $this->getDateByDays($arr['deadline_at'], $day, 1);
                }
                $db->insertAsDict("hr_probation_audit", $tmp);
            }

            //如果是上上级评估需要发送消息
            if ($arr['audit_level'] == self::AUDIT_LEVEL_2) {
                //插入消息表
                $msg_data = ['probation_audit_id' => $id,
                             'created_at'         => gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600),
                             'updated_at'         => gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600),
                ];
                $res      = $this->getDI()->get('db')->insertAsDict("hr_probation_message", $msg_data);
                if (!$res) {
                    throw new \Exception($this->getTranslation()->_('no_server'));
                }
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->getDI()->get('logger')->write_log('ProbationServer_audit---' . $e->getMessage() . '-----' . $e->getLine());
            return $this->checkReturn(['msg' => $e->getMessage()]);
        }
        //给上级发送push
        if (!empty($tmp)) {
            $this->push_notice_higher($tmp['audit_id'], $tmp['staff_info_id']);
        }

        //上上级不通过，给上级员工发送消息
        if ($arr['cur_level'] == self::CUR_LEVEL_SECOND && $arr['audit_level'] == 2 && $item['status'] == self::STATUS_NOT_PASS) {
            $this->sendMsgToManagerByStaffId($arr['staff_info_id'], $this->getManagerId($arr['staff_info_id']));
        }

        //记录转正日志
        if (isset($item['status']) && $item['status'] == self::STATUS_FORMAL) {
            $this->putFormalLog($audit_id, $arr['staff_info_id'], $arr['hp_status'], self::STATUS_FORMAL);
        }

        return $this->checkReturn(['data' => true]);


    }
	
	
	/**
	 * 获得分数
	 *
	 * @param     $score
	 * @param int $flag =0，除以1000，=1乘以1000
	 *
	 * @return float|int|string|null
	 */
	public function getScore($score, $flag = 0)
	{
		if (empty($flag)) {
			return bcdiv($score, 1000, 2);
		} else {
			return floatval($score) * 1000;
		}
	}
	
	/**
	 * 获得上级id
	 *
	 * @param $staff_info_id
	 *
	 * @return int|mixed
	 */
	public function getManagerId($staff_info_id)
	{
		$sql  = "
            select
                `value`
            from
                hr_staff_items
            where
                staff_info_id = :id and item='MANGER'
        ";
		$item = $this->getDI()->get("db_rby")->fetchOne($sql, \PDO::FETCH_ASSOC, ["id" => $staff_info_id]);
		if (empty($item)) {
			return 0;
		}
		return $item['value'];
	}
	
	/**
	 * 获得当前员工的一级部门ID及职等
	 *
	 * @param $staff_info_id
	 *
	 * @return array
	 */
	public function getDepartmentIdAndJobTitleLevel($staff_info_id)
	{
		$arr                      = [];
		$arr['sys_department_id'] = 0;
		$arr['job_title_level']   = 0;
		
		$sql  = "select sys_department_id,job_title_level from hr_staff_info where staff_info_id=:staff_info_id";
		$item = $this->getDI()->get("db_rbi")->fetchOne($sql, \PDO::FETCH_ASSOC, ["staff_info_id" => $staff_info_id]);
		if (!empty($item)) {
			return $item;
		}
		return $arr;
	}


    /**
     * 获取转正评估流程
     * @param $probation_id
     * @param $probationChannelType
     * @param array $probationData
     * @return array|void
     * @throws \ReflectionException
     */
	public function getProbationLogs($probation_id = null,$probationChannelType = null, array $probationData = [])
	{
		try {
			if (empty($probation_id)) {
				throw new \Exception($this->getTranslation()->_('miss_args'));
			}
            $dbName = $probationChannelType == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3 ? 'hr_probation_audit_contract_worker' : 'hr_probation_audit';
			$sql = "
            select
                *
            from
                $dbName
             where
                probation_id =:id
        ";
			
			
			$data = [];
			$arr  = $this->getDI()->get("db_rby")->query(
				$sql,
				["id" => $probation_id]
			)->fetchAll(\Phalcon\Db::FETCH_ASSOC);


            $remark          = "";
            $pic             = "";
            $comment         = ""; //第一次处理意见
            $staffNeedToHide = []; //需要隐藏的工号
            $lntVirtualApprovalId = (new SettingEnvServer())->getSetVal('lnt_virtual_approval_id');
            if (empty($arr)) {
                return ['code' => 1, "data" => $data, "pic" => $pic, 'remark' => $remark, 'comment' => $comment];
            }
            $staff_ids = array_column($arr, "audit_id");
            if ($lntVirtualApprovalId) {
                $staff_ids[] = $lntVirtualApprovalId;
            }
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('hsi.staff_info_id,hsi.name,job.job_name,d.name AS department_name,hsi.job_title');
            $builder->from(['hsi' => HrStaffInfoModel::class]);
            $builder->leftJoin(HrJobTitleModel::class, 'job.id = hsi.job_title', 'job');
            $builder->leftJoin(SysDepartmentModel::class, 'd.id = hsi.sys_department_id', 'd');
            $builder->inWhere('hsi.staff_info_id', $staff_ids);
            $staffArr = $builder->getQuery()->execute()->toArray();

            if (isCountry("ID")) {
                $jobTitleIdList = array_column($staffArr, 'job_title', 'staff_info_id');

                //获取需要隐藏的职位
                //Human Capital 职组 id = 15
                $workflowServer  = new WorkflowServer($this->lang, $this->timeZone);
                $workflowServer  = Tools::reBuildCountryInstance($workflowServer, [$this->lang, $this->timeZone]);
                $staffNeedToHide = $workflowServer->getHideStaffInfo($staff_ids, $jobTitleIdList);
            }

            $staffArr = array_column($staffArr, null, "staff_info_id");
            $curLevelMax = [];
            foreach ($arr as $k => $v) {
                $tmp                  = [];
                $tmp['probation_audit_id'] = $v['id'];
                $tmp['created_at']    = $v['created_at'];

                if (isCountry("ID") && in_array($v['audit_id'], $staffNeedToHide)) {
                    $tmp['staff_info_id'] = "";
                } else {
                    $tmp['staff_info_id'] = $v['audit_id'];
                }

                $tmp['name']            = '';
                $tmp['department_name'] = '';
                $tmp['job_title_name']  = '';
                $tmp['is_now']          = '0';
                $tmp['score']           = '';
                $tmp['is_active']       = $v['is_active'];

                //如果最后一个且是待评估
                if ($k == count($arr) - 1 && $v['audit_status'] == 1) {
                    $tmp['is_now'] = '1';
                }

                if (!empty($staffArr[$v['audit_id']])) {
                    if (isCountry("ID") && in_array($v['audit_id'], $staffNeedToHide)) {
                        $tmp['name'] = "";
                    } else {
                        $tmp['name'] = $staffArr[$v['audit_id']]['name'] ?? '';
                    }
                    $tmp['department_name'] = $staffArr[$v['audit_id']]['department_name'] ?? '';
                    $tmp['job_title_name']  = $staffArr[$v['audit_id']]['job_name'] ?? '';
                }

                $tmp['cur_level'] = $v['cur_level'];
                $curLevelMax[$tmp['cur_level']][] = $v['id'];
                if ($tmp['cur_level'] == self::CUR_LEVEL_SECOND) {
                    $tmp['audit_status_text'] = $this->getSecondStatusText($v['status'], $v['audit_status']);
                    //已处理
                    if ($v['audit_status'] == self::AUDIT_STATUS_DEAL) {
                        $remark = $v['remark'];
                        $pic    = $v['pic'];
                    }
                } else {
                    $comment                  = $v['remark'];
                    $tmp['audit_status_text'] = $this->getAuditStatusText($v['audit_status']);
                }

                //如果不是超时，算下分数
                if ($v['audit_status'] != self::AUDIT_STATUS_TIMEOUT) {
                    $temp = $this->getScoreFromTpl($v['score'], $v['cur_level']);
                    if ($tmp['cur_level'] == self::CUR_LEVEL_FIRST) {
                        $t_field = 'score';
                    } else {
                        $t_field = 'second_score';
                    }
                    $tmp['score'] = $temp[$t_field] ?? "";
                }
                $data[] = $tmp;
            }
            $returnData = $data;
            if (isCountry('MY') && $probationData) {
                //员工是lnt公司
                if ($probationData['contract_company_id'] == '-1') {
                    $existData = $this->addLntDataLog($lntVirtualApprovalId, $data, $probationData, $staffArr,
                        $curLevelMax ?? []);
                    if ($existData) {
                        $returnData = $existData;
                    }
                }
            }
			return ['code' => 1, "data" => $returnData,"pic" => is_null($pic) ? '': $pic, 'remark' => is_null($remark) ? '' : $remark, 'comment' => is_null($comment) ? '' : $comment];
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->write_log('ProbationServer_getTplItem---' . $e->getMessage() . '-----' . $e->getLine());
			return $this->checkReturn(['msg' => $e->getMessage()]);
		}
	}

    /**
     * MY 增加 LNT最后审批人
     * @param $lntVirtualApprovalId
     * @param $originData
     * @param $probationData
     * @param $staffArr
     * @param $curLevelMax
     * @return array
     */
    public function addLntDataLog($lntVirtualApprovalId, $originData, $probationData, $staffArr, $curLevelMax): array
    {
        if (empty($lntVirtualApprovalId)) {
            return [];
        }
        $curLevelFirst  = isset($curLevelMax[HrProbationModel::CUR_LEVEL_FIRST]) ? max($curLevelMax[HrProbationModel::CUR_LEVEL_FIRST]) : null;
        $curLevelSecond = isset($curLevelMax[HrProbationModel::CUR_LEVEL_SECOND]) ? max($curLevelMax[HrProbationModel::CUR_LEVEL_SECOND]) : null;
        if ($curLevelFirst && isset($probationData['first_audit_status'])
            && in_array($probationData['first_audit_status'],
                [HrProbationModel::FIRST_AUDIT_STATUS_DONE, HrProbationModel::FIRST_AUDIT_STATUS_TIMEOUT])
        ) {
            $addLntData[HrProbationModel::CUR_LEVEL_FIRST] = true;
        }
        if ($curLevelSecond && isset($probationData['second_audit_status'])
            && in_array($probationData['second_audit_status'],
                [HrProbationModel::SECOND_AUDIT_STATUS_DONE, HrProbationModel::SECOND_AUDIT_STATUS_TIMEOUT])
        ) {
            $addLntData[HrProbationModel::CUR_LEVEL_SECOND] = true;
        }
        if (empty($addLntData)) {
            return [];
        }
        $returnData = [];
        foreach ($originData as $item) {
            $item['created_at'] = date('Y-m-d', strtotime($item['created_at']));
            $returnData[] = $item;
            if (
                $curLevelFirst == $item['probation_audit_id'] && isset($addLntData[HrProbationModel::CUR_LEVEL_FIRST])
                || $curLevelSecond == $item['probation_audit_id'] && isset($addLntData[HrProbationModel::CUR_LEVEL_SECOND])
            ) {
                $addLnt                    = $item;
                $addLnt['created_at']      = $item['created_at'];
                $addLnt['staff_info_id']   = $lntVirtualApprovalId;
                $addLnt['name']            = $staffArr[$lntVirtualApprovalId]['name'];
                $addLnt['department_name'] = $staffArr[$lntVirtualApprovalId]['department_name'];
                $addLnt['job_title_name']  = $staffArr[$lntVirtualApprovalId]['job_name'];
                $returnData[]              = $addLnt;
            }
        }
        return $returnData;
    }
	
	/**
	 * 判断是否展示"激活评估表"
	 *
	 * @param $info array probation_audit 表的内容
	 *
	 * @return bool
	 */
	
	public function isCanActive($info = [])
	{
		try {
			if (empty($info)) {
				throw new \Exception($this->getTranslation()->_('miss_args'));
			}
            $ph_is_contract_staff         = isCountry('PH') && isset($info['hire_type']) && $info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_2;
            //是否已经激活过
//            if ($info['hp_active'] == 1) {
//                return false;
//            }

            //已通过，或者已转正不展示 （下方移过来的)
            if ($info['hp_status'] == self::STATUS_FORMAL) {
                return false;
            }
            //进入已处理页签且状态为（超时、未通过） 的评估表，评估页面个人信息上方新增按钮“激活”按钮，此按钮可一直存在直至点击后失效
            if ($ph_is_contract_staff && $info['probation_channel_type'] == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT) {
                if ($info['audit_status'] == self::AUDIT_STATUS_TIMEOUT && $info['first_audit_status'] == HrProbationModel::FIRST_AUDIT_STATUS_TIMEOUT) {
                    return true;
                } else {
                    return false;
                }
            }
            if ($ph_is_contract_staff && $info['probation_channel_type'] == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3) {
                //自己的audit_status
                if ($info['audit_status'] == self::AUDIT_STATUS_TIMEOUT) {
                    return true;
                } else {
                    return false;
                }
            }

            
			//待处理不展示
			if (in_array($info['audit_status'],[self::AUDIT_STATUS_PENDING,self::AUDIT_STATUS_DEAL])) {
				return false;
			}
            //自己通过不展示
            if ($info['status'] == self::STATUS_PASS) {
                return false;
            }
            if ($info['audit_status'] != self::AUDIT_STATUS_TIMEOUT){
                return false;
            }
			return true;
			
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->write_log('ProbationServer_isCanActive---' . $e->getMessage() . '-----' . $e->getLine());
			return false;
		}
		
	}

    /**
     * 判断是否展示"激活评估表"  大于 18 级的逻辑
     */

    public function isCanActiveV2( $info = [] ) {
        //如果是第二阶段 第一次评估不通过 并且第二次评估通过了 则不展示
        //是否已经激活过

        //已通过，或者已转正不展示
        if ($info['hp_status'] == self::STATUS_PASS || $info['hp_status'] == self::STATUS_FORMAL) {
            return false;
        }

        //第一阶段不展示
//        if ($info['cur_level'] == self::CUR_LEVEL_FIRST) {
//            return false;
//        }
        
        //待处理不展示
        if (in_array($info['audit_status'],[self::AUDIT_STATUS_PENDING,self::AUDIT_STATUS_DEAL])) {
            return false;
        }
        //自己通过不展示
        if ($info['status'] == self::STATUS_PASS) {
            return false;
        }

        //如果是第一次评估 并且没有通过么
        //判断第二次评估是否通过  通过不展示
        $arr = HrProbationAuditModel::findFirst([
                                             'conditions' => 'probation_id = :id:',
                                             'bind'       => [
                                                 'id' => $info['probation_id'] ?? '',
                                             ],
                                             'order' => 'id desc',
                                         ]);
        $arr = !empty($arr) ? $arr->toArray() : [];
        //判断第二次评估是通过或者待审批  通过不展示
        if (!empty($arr) && (
            in_array($arr['status'],[self::STATUS_PASS]) || in_array($arr['audit_status'],[self::AUDIT_STATUS_PENDING,self::AUDIT_STATUS_DEAL])
            ) ) {
            return false;
        }

        return true;
    }
	
	
	/**
	 * 根据分数模板算分数
	 *
	 * @param $score     string|array 分数模板
	 * @param $cur_level integer 第几次审批
	 *
	 * @return mixed|string
	 */
	public function getScoreFromTpl($score='', $cur_level='')
	{
		try {
			if (is_string($score)) {
				$scoreArr = json_decode($score, 1);
			} else {
				$scoreArr = $score;
			}
			if (empty($scoreArr)) {
				throw new \Exception('score field error'.json_encode($score).'  等级为=>'.$cur_level);
			}
			
			$field = 'score';
			if ($cur_level == 2) {
				$field = 'second_score';
			}
			$score_1 = 0;
			foreach ($scoreArr['list'] as $k => $v) {
				$t_score = 0;
				foreach ($v['list'] as $kk => $vv) {
					$t_score += floatval($vv[$field]) * floatval($vv['score_rule']);   //百分比
				}
				
				$scoreArr['list'][$k][$field] = "" . round($t_score / 100, 2);
				$score_1                      += $t_score * floatval($v['score_rule']);
			}
			$scoreArr[$field] = "" . round($score_1 / 10000, 2);
			return $scoreArr;
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->write_log('ProbationServer_isCanActive---' . $e->getMessage() . '-----' . $e->getLine(),'info');
			return [];
		}
		
		
	}
	
	
	/**
	 * 获得日期
	 *
	 * @param     $date
	 * @param     $days 40
	 * @param int $flag 0=40天之前，1=40天之后
	 *
	 * @return false|string
	 */
	public function getDateByDays($date, $days, $flag = 0)
	{
		
		$default = '-';
		if (!empty($flag)) {
			$default = '+';
		}
		return date("Y-m-d", strtotime($default . $days . " days", strtotime($date)));
	}
	
	
	/**
	 * 获得审核状态=1待处理，2已处理，3超时
	 *
	 * @param $id
	 */
	public function getAuditStatusText($id)
	{
		return $this->getTranslation()->_("probation_audit_status_" . $id);
	}
	
	/**
	 * 获得第二次评审时候的文字
	 *
	 * @param $status
	 * @param $audit_status
	 */
	public function getSecondStatusText($status, $audit_status)
	{
		//还没评估
		if (empty($status)) {
			return $this->getAuditStatusText($audit_status);
		}
		
		return $this->getStatusText($status);
	}
	
	
	/**
	 * 获得状态=1试用期，2已通过，3未通过，4已转正
	 *
	 * @param $id
	 */
	public function getStatusText($id)
	{
		return $this->getTranslation()->_("probation_status_" . $id);
	}
	
	
	/**
	 * @param $staff_id         //接收消息staff_id
	 * @param $content_staff_id //content 内容中的staff_id
	 */
	public function push_notice_higher($staff_id, $content_staff_id)
	{
		/**
		 * "{"locale":"en","data":{"staff_info_id":"17074","src":"1","message_title":"需要push的标题","message_content"："需要push的内容","message_scheme":"消息的scheme"}}"
		 */
		try {
			$approval_html_url  = $this->get_setting_by_code('probation_html_url');
			$approval_index_url = $this->get_setting_by_code('probation_index_url');
			
			$data                    = [];
			$data['staff_info_id']   = $staff_id;
			$data['src']             = 'backyard';
			$data['message_title']   = $this->getMsgTemplateByUserId($staff_id, 'hr_probation_push_to_higher_title');
			$data['message_content'] = sprintf($this->getMsgTemplateByUserId($staff_id, 'hr_probation_push_to_higher_content'), $content_staff_id);
			$data['message_scheme']  = $approval_html_url . urlencode($approval_index_url);
			$this->getDI()->get('logger')->write_log('push_notice_higher_pushMessage params:' . json_encode($data), 'info');
			$ret = (new ApiClient('bi_rpc', '', 'push_to_staff'));
			$ret->setParams($data);
			$_data = $ret->execute();
			$this->getDI()->get('logger')->write_log("push_notice_higher:pushMessage-return- " . json_encode($_data), 'info');
			if ( !isset($_data['result']) || !$_data['result']) {
				$this->getDI()->get('logger')->write_log("push_notice_higher_error: params==>" . json_encode($data) . ' return ==> ' . json_encode($_data), 'info');
			}
			return true;
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->write_log('push_notice_higher probation push  error===' . $e->getMessage());
			return false;
		}
		
	}
	
	/**
	 *
	 * env 过长 或经常改动 推荐用页面配置相关env 数据
	 *
	 * @param $code
	 *
	 * @return bool
	 */
	public function get_setting_by_code($code)
	{
		try {
			if (empty($code)) return false;
			
			$sql = " select set_val from setting_env where code = '{$code}'";
			return $this->getDI()->get('db_rbi')->fetchColumn($sql);
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->write_log('get_setting_by_code===' . $e->getMessage());
			return '';
		}
	}
	
	
	/**
	 * 根据用户id，
	 *
	 * @param       $user_id
	 * @param       $template_name
	 * @param array $bind
	 *
	 * @return string
	 */
	public function getMsgTemplateByUserId($user_id, $template_name, $bind = [])
	{
		try {
            $lang = (new StaffServer())->getLanguage($user_id);
			$lang = substr($lang, 0, 2);
			if ($lang == 'zh') {
				$lang = 'zh-CN';
			}
			$this->getDI()->get('logger')->write_log('user_id===' . $user_id . "===lang=" . $lang, 'info');
			$t          = $this->getTranslation($lang);
			return $t->_($template_name, $bind);
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->write_log('getMsgTemplateByUserId===' . $e->getMessage());
			return '';
		}
	}
    public function getProbationDbName($probationChannelType): array
    {
        $dbName  = 'hr_probation';
        $auditDbName = 'hr_probation_audit';
        if ($probationChannelType == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3) {
            $dbName  = 'hr_probation_contract_worker';
            $auditDbName = 'hr_probation_audit_contract_worker';
        }
        return [$dbName, $auditDbName];
    }

    public function sendAckNowledgement($params)
    {
        $hrProbationCondition = [
            'conditions' => 'id = :id:',
            'bind' => [
                'id' => $params['probation_id'],
            ],
        ];
        if (!empty($params['probation_channel_type']) && $params['probation_channel_type'] == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3) {
            $hrProbation = HrProbationContractWorkerModel::findFirst($hrProbationCondition);
        } else {
            $hrProbation = HrProbationModel::findFirst($hrProbationCondition);
        }
        $hrProbation = $hrProbation ? $hrProbation->toArray() : [];
        $db = $this->getDI()->get("db");
        if ($hrProbation) {
            $probationChannelType = $params['probation_channel_type'] ?? HrProbationModel::PROBATION_CHANNEL_TYPE_FORMAL;
            [$dbName,$auditDbName] = $this->getProbationDbName($probationChannelType);
            $src = env('sign_url') . "/#/employeesPush?id=" . $params['id'] . "&probation_id=" . $params['probation_id']. "&cul_level=" . $params['cul_level']. "&probation_channel_type=" .$probationChannelType;
            $html = "<meta name=\"viewport\" content=\"width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no\" /><iframe src='{$src}' width='100%' height='85%'></iframe>";

            $param = [];
            $param['staff_users'] = [$hrProbation['staff_info_id']];//数组 多个员工id
            $param['message_title'] = $this->getMsgTemplateByUserId($hrProbation['staff_info_id'], 'probation_acknowledgement');
            $param['message_content'] = $html;
            $param['staff_info_ids_str'] = $hrProbation['staff_info_id'];
            $param['id'] = time() . $hrProbation['staff_info_id'] . rand(1000000, 9999999);
            $param['category'] = MessageEnums::MESSAGE_CATEGORY_71;//消息为 71 转正评估跟进结果 发送给本人
            $param['related_id'] = strtotime(' +2 day midnight');
            $this->getDI()->get('logger')->write_log('sendAckNowledgement :param:' . json_encode($param), 'info');
            $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
            $bi_rpc->setParams($param);
            $res = $bi_rpc->execute();
            if ($res && $res['result']['code'] == 1) {
                $kitId = $res['result']['data'][0];

                $db->updateAsDict($auditDbName, [
                    'acknowledgement_message_id' => $kitId,
                ], ["conditions" => "id=" . intval($params['id'])]);
            }

            $data = [
                "staff_info_id" => $hrProbation['staff_info_id'],  //
                "src" => "backyard",      //1:'kit'; 2:'backyard','c';
                "message_title" => $this->getMsgTemplateByUserId($hrProbation['staff_info_id'], 'probation_acknowledgement'),  //标题
                "message_content" =>  $this->getMsgTemplateByUserId($hrProbation['staff_info_id'], 'probation_acknowledgement_content'),//内容
                "message_scheme" => '', //地址
            ];
            $ret = (new ApiClient('bi_rpc', '', 'push_to_staff'));
            $ret->setParams($data);
            $bi_return = $ret->execute();
            $this->getDI()->get('logger')->write_log('sendAckNowledgement :param:' . json_encode($data, JSON_UNESCAPED_UNICODE) . " push_result " . json_encode($bi_return, JSON_UNESCAPED_UNICODE), 'info');
        }

    }

    // 被评估人 签字
    public function signProbation($params)
    {
        if ($params['probation_channel_type'] == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3) {
            $hrProbationModel = HrProbationAuditContractWorkerModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['id']],
            ]);
            $auditDbname = 'hr_probation_audit_contract_worker';
            $probationDbName = 'hr_probation_contract_worker';
        } else {
            $hrProbationModel = HrProbationAuditModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['id']],
            ]);
            $auditDbname = 'hr_probation_audit';
            $probationDbName = 'hr_probation';
        }
        // 将消息 已读

        if ($hrProbationModel) {
            $db = $this->getDI()->get("db");
            $db->updateAsDict($auditDbname, [
                'sign_url'           => $params['sign_url'],
                'option_type'        => $params['option_type'],
                'acknowledge_remark' => $params['acknowledge_remark'],
            ], ["conditions" => "id=" . intval($params['id'])]);

            $db->updateAsDict($probationDbName, [
                'sign_url'  => $params['sign_url'],
                'sign_time' => gmdate("Y-m-d H:i:s"),
            ], ["conditions" => "id=" . intval($hrProbationModel->probation_id)]);
            $staffServer =
                new StaffServer($this->lang, $this->timeZone);
            $staffInfo =
                $staffServer->getStaffInfoById($hrProbationModel->staff_info_id);
            // 向上级发送消息
            $param = [];
            $param['staff_users'] = [$hrProbationModel->follow_staff_id];//数组 多个员工id
            $param['message_title'] = $this->getMsgTemplateByUserId($hrProbationModel->follow_staff_id, 'probation_sign_acknowledgement');
            //最后工作日
            $lastWorkDate = date('Y-m-d',strtotime($this->getLeaveDateForAck($staffInfo). " -1 day"));
            $param['message_content'] = "<div style='font-size: 40px'>" . sprintf(
                    $this->getMsgTemplateByUserId($hrProbationModel->follow_staff_id, 'probation_sign_acknowledgement_content'),
                    $staffInfo['staff_name'] . "(" . $staffInfo['staff_info_id'] . ")",
                    $lastWorkDate
                ) . "</div>";
            $param['staff_info_ids_str'] = $hrProbationModel->follow_staff_id;
            $param['id'] = time() . $hrProbationModel->follow_staff_id . rand(1000000, 9999999);
            $param['category'] = -1;
            $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
            $bi_rpc->setParams($param);
            $res = $bi_rpc->execute();
            $this->getDI()->get('logger')->write_log('signProbation send_to_manger :param:' . json_encode($param, JSON_UNESCAPED_UNICODE) . " " . json_encode($res, JSON_UNESCAPED_UNICODE), 'info');

            // 发送 邮件
            $this->sendEmailAttachment($hrProbationModel->probation_id,$params['probation_channel_type']);
            if (!(isCountry('PH')  && $staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_2)) {
                // 发送消息给 er
                $this->sendProbationSignMessage($hrProbationModel->staff_info_id);
            }
            // 进hold
            $this->holdStaff($hrProbationModel->staff_info_id);
            //同步员工状态
            $this->syncStaffState($hrProbationModel->staff_info_id);
        } else {
            throw new \Exception('signProbation not found  params ' . json_encode($params, JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     * @param $staffId
     * @return void
     * @throws \Exception
     */
    public function sendProbationSignMessage($staffId)
    {
        $staffServer = new StaffServer($this->lang, $this->timeZone);
        $staffInfo = $staffServer->getStaffInfoById($staffId);

        if ($staffInfo) {
            $workflowServer = new WorkflowServer($this->lang, $this->timeZone);
            $erIds = $workflowServer->findJurisdictionAreaStaffIds($staffInfo['node_department_id'], ['store_id' => $staffInfo['sys_store_id']], 0, 80);
            $erIds = explode(',', $erIds);
            $erIds[] = $staffId;
            $erIds = array_values(array_filter($erIds));

            $pdfHeaderFooterSetting = [
                'format' => 'a4',
                'displayHeaderFooter' => true, //是否开启页眉页脚，
                'headerTemplate' => '',//页眉内容，当开启的时候（displayHeaderFooter=true）该字段才有用
                'footerTemplate' => '',//页脚内容，当开启的时候（displayHeaderFooter=true）该字段才有用
            ];
            $pdfHeaderFooterSetting['headerTemplate'] = '<table
  style=" width: 190mm;font-size: 3.5mm; font-weight: 700;font-family: sans-serif;text-align: center; margin: 0 12mm;max-height:18mm ;"
  border="0">
  <tr>
    <td style="text-align: initial;"><strong style="font-size: 4.5mm;">FLASH EXPRESS (PH) CO., LTD.
        INC.</strong>
    </td>
    <td style="width: 35mm;"><img style="width: 35mm;height: auto;object-fit: cover;"
        src="data:image/png;base64,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"
        alt="" /></td>
  </tr>

</table>';

            $html_path = BASE_PATH.'/public/pdf_template/end_of_probation.ftl';
            //上传oss 更新表
            $result = $this->uploadFileOss($html_path , self::OSS_DIR_MAPS[self::HRIS_STAFF_PROFILE]);
            $html_oss_url = $result['object_url'];//oss地址
            $formPdfServer = new formPdfServer();
            $add_hour = $this->getDI()['config']['application']['add_hour'];
            $leave_date = $this->getLeaveDateForAck($staffInfo);
            $pdf_path = $formPdfServer->generatePdf($html_oss_url, [
                'staff_name' => $staffInfo['staff_name'],
                'department_name' => $staffInfo['department_name'],
                'job_name' => $staffInfo['job_name'],
                'send_date' => gmdate("Y-m-d H:i:s", time() + $add_hour * 3600),
                'leave_date' => $leave_date, // 14天后离职
            ], [], '',$pdfHeaderFooterSetting);
            if ($pdf_path) {

                $data = [
                    "staff_info_id" => $staffId,  //上级id
                    "src" => "backyard",      //1:'kit'; 2:'backyard','c';
                    "message_title" => $this->getTranslation('en')->_('probation_end_of_notice_title'),  //标题
                    "message_content" =>  '',//内容
                    "message_scheme" => '', //地址
                ];
                $ret = (new ApiClient('bi_rpc', '', 'push_to_staff'));
                $ret->setParams($data);
                $bi_return = $ret->execute();
                $this->getDI()->get('logger')->write_log('probation_end_of_notice sendPush'  . json_encode([
                        'staffs' => $staffId ,
                        'params' => json_encode($data, JSON_UNESCAPED_UNICODE),
                        'result' => json_encode($bi_return, JSON_UNESCAPED_UNICODE),
                    ]), 'info');

                foreach ($erIds as $erId) {

                    $kit_param['message_title'] = $this->getTranslation('en')->_('probation_end_of_notice_title');
                    $content = "<meta name=\"viewport\" content=\"width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no\" /><iframe src='https://hr-api.flashexpress.com/vpdf/web/viewer.html?file={$pdf_path['object_url']}' width='100%' height='85%'></iframe>";
                    $kit_param['message_content'] = $content;
                    $kit_param['staff_info_ids_str'] = $erId;
                    $kit_param['add_userid'] = $erId;
                    $kit_param['staff_users'] = array(0 => array('id' => $erId));
                    $kit_param['category'] = -1;
                    $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
                    $bi_rpc->setParams($kit_param);
                    $res = $bi_rpc->execute();
                    $this->getDI()->get('logger')->write_log('probation_end_of_notice sendMessage ' . $erId . ' '  . json_encode([
                            'staffs' => implode(',', $erIds) ,
                            'params' => json_encode($kit_param, JSON_UNESCAPED_UNICODE),
                            'result' => json_encode($res, JSON_UNESCAPED_UNICODE),
                        ]), 'info');
                }
            }

        }

    }

    /**
     * 发送评估 pdf
     * 这里合同工五个月需要换表，不换表的$probationChannelType 已查询出来的为主，逻辑判断使用查询后的
     * @param $probationId
     * @param $probationChannelType
     * @return void
     * @throws \Exception
     */
    public function sendEmailAttachment($probationId,$probationChannelType = null)
    {
        if ($probationChannelType == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3) {
            $hrProbationModel = HrProbationContractWorkerModel::class;
            $item = HrProbationAuditContractWorkerModel::findFirst([
                'conditions' => 'probation_id = ?1',
                'order' => 'id desc',
                'bind'       => [
                    1 =>  $probationId,
                ],
            ]);
        } else {
            $hrProbationModel = HrProbationModel::class;
            $item = HrProbationAuditModel::findFirst([
                'conditions' => 'probation_id = ?1',
                'order' => 'id desc',
                'bind'       => [
                    1 =>  $probationId,
                ],
            ]);
        }
        $db = $this->getDI()->get("db");
        $builder = $this->modelsManager->createBuilder();
        $execute = $builder->columns([
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.personal_email',
            'hsi.job_title_level',
            'hsi.job_title_grade_v2',
            'hsi.hire_date',
            'hjt.job_name',
            's.name as department_name',
            'n.name as node_department_name',
            'hp.id as probation_id',
            'hp.formal_at',
            'hp.sign_url',
            'hp.is_delay',
            'hp.hire_type',
            'hp.first_evaluate_end',
            'hp.second_evaluate_end',
            'hp.probation_channel_type',
            'hp.contract_formal_date',
        ])->from(['hsi' => HrStaffInfoModel::class])
            ->leftJoin($hrProbationModel, 'hp.staff_info_id = hsi.staff_info_id', 'hp')
            ->leftJoin(HrJobTitleModel::class, 'hjt.id = hsi.job_title', 'hjt')
            ->leftJoin(SysDepartmentModel::class, 'n.id = hsi.node_department_id', 'n')
            ->leftJoin(SysDepartmentModel::class, 's.id = hsi.sys_department_id', 's')
            ->where('hp.id = :id:', ['id' => $probationId])->getQuery()->getSingleResult();
        if ($execute) {
            $data = $execute->toArray();
            $data['hire_date'] = $this->getDateByDays($data['hire_date'], 0, 1);
            $data['hire_text'] = 'On Board Date';
            if ($data['probation_channel_type'] == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_TO_FORMAL) {
                $data['hire_date'] = $data['contract_formal_date'];
                $data['hire_text'] = 'Date of Become the Regular Employee';
            }

            $data['first_deadline_date'] = $data['first_evaluate_end'];
            $data['second_deadline_date'] = $data['second_evaluate_end'];
            if ($data['probation_channel_type'] == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT) {
                $data['second_deadline_date'] = '';
            }
            if ($item) {
                $item = $item->toArray();
                $tpl_id = $item['tpl_id'];
                $score = $item['score'];
            } else {
                $tpl_id = $data['probation_channel_type'] == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT ? 1 : $this->getTplIdByJobTitleGradeV2($data['job_title_grade_v2']);
                $score = null;
            }
            $this->lang = 'en';
            $tpl = $this->getTplItem($tpl_id, $score);
            if (is_string($tpl)) {
                throw new \Exception($tpl);
            }

            $data['score'] = $tpl['score'];
            $data['question_num'] = $tpl['question_num'];
            $data['score_rule_text'] = $tpl['score_rule_text'];
            $data['tpl_name'] =  $this->getTranslation('en')->_('probation_tpl_name_'.$tpl_id);;
            $data['option_type'] = (int) $item['option_type'];
            $data['acknowledge_remark'] = (string) $item['acknowledge_remark'] ;
            $data['comment'] = $this->getProbationLogs($probationId,$probationChannelType)['comment'];
            $data['attendance'] = $this->getAttendance($data['staff_info_id'],$probationChannelType);
            $data['alert'] = $this->getAlert($data['staff_info_id'], $data['hire_date'], $data['formal_at']);
            $img_data = [['name'=>'staff_sign_img','url'=> (string) $data['sign_url']]];

            //todo 上传html模板
            $html_path = BASE_PATH.'/public/pdf_template/probation_staff.ftl';
            //上传oss 更新表
            $result = $this->uploadFileOss($html_path , self::OSS_DIR_MAPS[self::HRIS_STAFF_PROFILE]);
            $html_oss_url = $result['object_url'];//oss地址


            $pdfHeaderFooterSetting = [
                'format' => 'a4',
                'displayHeaderFooter' => true, //是否开启页眉页脚，
                'headerTemplate' => '',//页眉内容，当开启的时候（displayHeaderFooter=true）该字段才有用
                'footerTemplate' => '',//页脚内容，当开启的时候（displayHeaderFooter=true）该字段才有用
            ];
            $pdfHeaderFooterSetting['headerTemplate'] = '<table
  style=" width: 100%;font-size: 3.5mm; font-weight: 700;font-family: sans-serif;text-align: center; margin: 0 12mm;max-height:23mm;"
  border="0">
  <tr>
    <td style="text-align: initial;"><strong style="font-size: 4mm;">FLASH EXPRESS (PH) CO., LTD.
        INC.</strong>
    </td>
    <td style="width: 35mm;"><img style="width: 35mm;height: auto;object-fit: cover;"
        src="data:image/png;base64,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"
        alt="" /></td>
  </tr>

</table>';


            $formPdfServer =
            new formPdfServer();
            $pdf_path = $formPdfServer->generatePdf($html_oss_url, $data, $img_data, '', $pdfHeaderFooterSetting);
            $probationTableName = $data['probation_channel_type'] == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3 ? 'hr_probation_contract_worker' : 'hr_probation';
            $db->updateAsDict($probationTableName, [
                'pdf_path' => $pdf_path ? $pdf_path['object_url'] : '',
            ], ["conditions" => "id=" . $probationId]);
            $emails = [];
            if ($data['hire_type'] != HrStaffInfoModel::HIRE_TYPE_2) {
                $settingEnvServer = new SettingEnvServer();
                $emails = $settingEnvServer->getSetVal('er_email');
                $emails = explode(',', $emails);
                if ($data['personal_email']) {
                    $emails[] = $data['personal_email'];
                }
            }
            if ($pdf_path && $emails) {
                $offer_attachment_path = sys_get_temp_dir() . "/" . md5( time()) . '.pdf';
                file_put_contents($offer_attachment_path,file_get_contents($pdf_path['object_url']));
                $sendEmail = \FlashExpress\bi\App\library\Mail::send(
                    $emails,
                    $this->getTranslation('en')->_('probation_sign_email_title'),
                    $this->getTranslation('en')->_('probation_sign_email_content'),
                    $offer_attachment_path,
                    'Acknowledgment of Probation Evaluation.pdf');
                unlink($offer_attachment_path);
            }

            $this->getDI()->get('logger')->write_log('probation sendEmailAttachment '  . json_encode([
                    'email' => $emails,
                    'attachment' => $pdf_path,
                    'result' => $sendEmail ?? false,
                    'probation_channel_type' => $data['probation_channel_type'],
                    'probation_id' => $probationId,
                ]), 'info');
        }
    }
    /**
     * 获得警告信
     * @param $staff_info_id
     * @param $hire_date
     * @param $formal_at
     * @return array
     */
    public function getAlert($staff_info_id, $hire_date, $formal_at): array
    {

        $query = MessageWarningModel::query()->columns(['type_code','date_ats as date_at'])->where('staff_info_id = :id: and is_delete=0',['id' => $staff_info_id]);

        if (!empty($hire_date)) {
            $query->andWhere('date_at>=:start:' , ['start' => $hire_date]);
        }

        if (!empty($formal_at)) {
            $query->andWhere('date_at<=:end:' , ['end' => $formal_at]);
        }
        $query->orderBy('id DESC')->limit(2);
        $arr = $query->execute()->toArray();
        foreach ($arr as $k => $v) {
            $arr[$k]['type_code_text'] = $this->getTranslation('en')->_($v['type_code']);//需要加语言
        }
        return $arr;
    }
    /**
     * @param array $type_ids
     * @param bool $is_deleted true=需要已删除的数据
     * @return array
     */
    public function get_violation_reason_type(array $type_ids = [], bool $is_deleted = true): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['t_id,t_key,remark,type']);
        $builder->from(ViolationReasonTypeModel::class);
        if (!$is_deleted) {
            $builder->andWhere('is_deleted = :is_deleted:', ['is_deleted' => ViolationReasonTypeModel::UN_DELETED]);
        }
        if (!empty($type_ids) && is_array($type_ids)) {
            $builder->andWhere('type in ({type_ids:array})', ['type_ids' => $type_ids]);
        }
        $builder->groupBy('t_id');
        $builder->orderBy('sort desc,id desc');
        $list = $builder->getQuery()->execute()->toArray();
        foreach ($list as $k => $v) {
            $list[$k]['t_key_text'] = self::$t->_($v['t_key']);
        }
        return $list;
    }
    public function getAttendance($staff_info_id,$probationChannelType=null): array
    {
        $query = $probationChannelType == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3
            ? HrProbationAttendanceContractWorkerModel::query() : HrProbationAttendanceModel::query();
        $temp = $query->where('staff_info_id = :id:',['id' => $staff_info_id])->execute()->toArray();
        $data = [];
        $data[1]['late'] = "";
        $data[1]['sick'] = "";
        $data[1]['casual'] = "";
        $data[1]['lack'] = "";

        $data[2]['late'] = "";
        $data[2]['sick'] = "";
        $data[2]['casual'] = "";
        $data[2]['lack'] = "";

        if (!empty($temp)) {
            foreach ($temp as $item) {
                $data[$item['cur_level']]['late'] = (string) $item['late'];
                $data[$item['cur_level']]['sick'] = (string) $item['sick'];
                $data[$item['cur_level']]['casual'] = (string) $item['casual'];
                $data[$item['cur_level']]['lack'] = (string) (empty($item['lack']) ? $item['lack'] : bcdiv((int)$item['lack'],10,2));  // ******** 产品要求 /10
            }
        }
        return $data;
    }
    /**
	 * 获得该用户移动设备最新的语言环境
	 *
	 * @param $staff_info_id
	 *
	 * @return array|string
	 */
	public function getLatestMobileLang($staff_info_id)
	{
		try {
			static $langArr = [];
			if (empty($langArr[$staff_info_id])) {
				$sql = "select accept_language from staff_account where staff_info_id =:staff_info_id order by updated_at desc";
				$arr = $this->getDI()->get("db_fle")->fetchOne($sql, \PDO::FETCH_ASSOC, ["staff_info_id" => (int)$staff_info_id]);
				if (empty($arr) || empty($arr['accept_language'])) {
					$langArr[$staff_info_id] = 'th';
				} else {
					$langArr[$staff_info_id] = $arr['accept_language'];
				}
			}
			return $langArr[$staff_info_id];
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->write_log('getLatestMobileLang===' . $e->getMessage());
			return 'th';
		}
	}



	/**
	 * 给未通过的上级员工以及被评估员工所属HRBP发送消息
	 *
	 * @param int $staff_info_id
     * @param $audit_id  -- 员工上级
	 */
	public function sendMsgToManagerByStaffId(int $staff_info_id=0, $audit_id=0)
	{
		try {
			$return = false;
			$message_staff_ids = [];
			//找上级
			$staffAllData = (new StaffServer())->getHigherStaffId($staff_info_id);
			if(isset($staffAllData['value']) && !empty($staffAllData['value'])){
				$message_staff_ids[] = $staffAllData['value'];
			}
			//找员工的部门和网点
			$staffInfo = HrStaffInfoModel::findFirst([
				                                         'conditions' => "staff_info_id = :staff_info_id:",
				                                         'bind' => [
					                                         'staff_info_id' => $staff_info_id,
				                                         ],
				                                         'columns' => 'name,staff_info_id,sys_department_id,sys_store_id',
			                                         ]);
			if(empty($staffInfo)) {
				throw new \Exception('sendMsgToManagerByStaffId 没有找到该用户! $staff_info_id=>' . $staff_info_id);
			}
			//找 hrbp;
			$WorkflowServer = new WorkflowServer($this->lang, $this->timeZone);
			$HRBP_ids       = $WorkflowServer->findHRBP($staffInfo->sys_department_id, ["store_id" => $staffInfo->sys_store_id]);
			$message_staff_ids = array_merge(explode(',', $HRBP_ids),$message_staff_ids);
			$message_staff_ids =  array_values(array_unique($message_staff_ids));
			//如果存在可以发送的人
			if(!empty($message_staff_ids)){

				foreach($message_staff_ids as $k=>$v){
					$id = time() . $v . rand(1000000, 9999999);
					$kit_param                       = [];
					$kit_param['staff_users']        = [$v];//数组 多个员工id
					$kit_param['message_title']      = $this->getMsgTemplateByUserId($v, 'hr_probation_field_msg_to_staff_title');
					$kit_param['message_content']    = addslashes("<div style='font-size: 30px'>" .$this->getMsgTemplateByUserId($v, 'hr_probation_field_msg_to_not_pass', ['name' => $staffInfo->name,'staff_info_id'=>$staff_info_id]). "</div>");
					$kit_param['staff_info_ids_str'] = $v;
					$kit_param['add_userid']         = $audit_id;
					$kit_param['id'] = $id;
					$kit_param['category']           = -1;
					$bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message', $this->lang));
					$bi_rpc->setParams($kit_param);
					$res = $bi_rpc->execute();
					$this->getDI()->get('logger')->write_log('sendMsgToManagerByStaffId-result:-param:' . json_encode($kit_param).'-return-' . json_encode($res), 'info');
					if ($res && $res['result']['code'] == 1) {
						$return = true;
					}else{
						$this->getDI()->get('logger')->write_log('sendMsgToManagerByStaffId 发送不通过消息失败$staff_info_id==>'.$staff_info_id.'! param ==> '.json_encode($kit_param).' result ==>'.json_encode($res));
					}

				}


			}
			return $return;
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->write_log('sendMsgToManagerByStaffId===' . $e->getMessage());
			return false;
		}

	}


	/**
	 * 记录转正日志
	 * @param $operaterId
	 * @param $staffInfoId
	 * @param $beforeStatus
	 * @param $afterStatus
	 * @return mixed
	 */

	public function putFormalLog($operaterId='', $staffInfoId='', $beforeStatus='', $afterStatus='')
	{
		try {
            $before                             = [];
			$before['body']                     = [];
			$before['body']['staff_info_id']    = $staffInfoId;
			$before['body']['probation_status'] = $beforeStatus;

			$after                             = $before;
			$after['body']['probation_status'] = $afterStatus;


            $data =[
                    "operater" => $operaterId,
                    "staff_info_id" => $staffInfoId,
                    "type" => 'probation',
                    "before" => json_encode($before, JSON_UNESCAPED_UNICODE),
                    "after" => json_encode($after, JSON_UNESCAPED_UNICODE),
            ];
            $client = new ApiClient('hr_rpc', '', 'add_operate_logs');
            $client->setParams($data);
            return  $client->execute();
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->write_log('sendMsgToManagerByStaffId===' . $e->getMessage());
			return false;
		}

	}

    /**
     * @param $hp_probation_id --  列表id
     * @param $audit_id  --审核人id
     * @throws ValidationException
     */
    public function active( $hp_probation_id, $audit_id ,$probationChannelType = HrProbationModel::PROBATION_CHANNEL_TYPE_FORMAL) {

        if (empty($hp_probation_id) || empty($audit_id)) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        [$dbName,$auditDbName] = $this->getProbationDbName($probationChannelType);
        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            $auditCondition = [
                'conditions' => 'id = :id: and audit_id = :audit_id: ',
                'bind'       => ['id' => $hp_probation_id, 'audit_id' => $audit_id],
            ];
            $HrProbationAuditArr = $probationChannelType == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3
                ? HrProbationAuditContractWorkerModel::findFirst($auditCondition)
                : HrProbationAuditModel::findFirst($auditCondition);

            if (empty($HrProbationAuditArr)) {
                throw new \Exception($this->getTranslation()->_('miss_args'));
            }
            $arr                 = $HrProbationAuditArr = $HrProbationAuditArr->toArray();
            $probationCondition = [
                'conditions' => 'id = :probation_id: ',
                'bind'       => ['probation_id' => $arr['probation_id']],
            ];
            $HrProbationModelArr = $probationChannelType == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3
                ? HrProbationContractWorkerModel::findFirst($probationCondition) : HrProbationModel::findFirst($probationCondition);
            $HrProbationModelArr = !empty($HrProbationModelArr) ? $HrProbationModelArr->toArray() : [];
            $arr['first_audit_status'] = $HrProbationModelArr['first_audit_status'];
            $arr['hp_status'] = $HrProbationModelArr['status'] ?? '';
            $arr['hire_type'] = $HrProbationModelArr['hire_type'];
            $arr['formal_at'] = $HrProbationModelArr['formal_at'];
            $arr['hp_active'] = $HrProbationModelArr['is_active'] ?? '';
            $arr['probation_channel_type'] = $HrProbationModelArr['probation_channel_type'];
            //激活时如果 score 为空 从新获取
            if (empty($arr['score'])) {
                $tpl = $this->getTplItem($arr['tpl_id'], $arr['score']);
                if (empty($tpl['code'])) {
                    throw new \Exception(json_encode($tpl));
                }
                $arr['score'] = $tpl['score'];
            }
            //获取员工信息
            $staffInfo = HrStaffInfoModel::findFirst([
                                                         'conditions' => 'staff_info_id = :staff_info_id:',
                                                         'bind'       => [
                                                             'staff_info_id' => $arr['staff_info_id'] ?? '',
                                                         ],
                                                         'columns'    => 'name,staff_info_id,hire_date,sys_store_id,job_title,sys_department_id,job_title_grade_v2',
                                                     ]);
            $staffInfo = !empty($staffInfo) ? $staffInfo->toArray() : [];
            $arr       = array_merge($arr, $staffInfo);
            $flag      = $this->isCanActive($arr);
            if (empty($flag)) {
                throw new ValidationException($this->getTranslation()->_('disable_active'));
            }
            //这里重新赋值
            $arr = $HrProbationAuditArr;

            //待处理
            $arr['audit_status'] = self::AUDIT_STATUS_PENDING;
            $arr['status']       = 0;
            $arr['created_at']   = date("Y-m-d H:i:s");
            $arr['updated_at']   = $arr['created_at'];
            $start = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
            $arr['deadline_at']        = $this->getDateByDays($start, 3, 1);
            $arr['is_active']          = 0;
            unset($arr['id']);
            $probation_update = ['is_active' => HrProbationModel::IS_ACTIVE_YES];
            if ($HrProbationAuditArr['cur_level'] == self::CUR_LEVEL_FIRST){
                $probation_update['first_audit_status'] = HrProbationModel::FIRST_AUDIT_STATUS_RUN;
            }else{
                $probation_update['first_audit_status'] = $HrProbationModelArr['first_audit_status'];
                $probation_update['second_audit_status'] = HrProbationModel::SECOND_AUDIT_STATUS_RUN;
                $arr['second_deadline_at'] = $this->getDateByDays($start, 3, 1);
            }
            //菲律宾 合同工 上方的$probation_update 赋值 合同工也在使用 谨慎修改
            if (isCountry('PH') && $HrProbationModelArr['hire_type'] == HrStaffInfoModel::HIRE_TYPE_2) {
                $probation_update['audit_status'] =self::AUDIT_STATUS_PENDING;
            }
            $db->updateAsDict($auditDbName, ['is_active'=>1], ["conditions" => 'id = '.$hp_probation_id.' and audit_id = '.$audit_id]);
            $db->insertAsDict($auditDbName, $arr);
            $db->updateAsDict($dbName, $probation_update, ["conditions" => 'id=' . intval($arr['probation_id'])]);
            $db->commit();
        }catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }
        return $this->checkReturn(['data' => true]);
    }

    /**
     * 获取月薪制合同工
     * @param $hireDate
     * @param $type
     * @return array
     */
    public function getMonthContractStaffIds($hireDate,$type): array
    {
        $dbName = $type == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3 ? 'hr_probation_contract_worker' : 'hr_probation';
        $sql = "
            select
                hsi.staff_info_id,
                hsi.job_title_level,
                hsi.job_title_grade_v2,
                hsi.hire_date,
                hsi.manger as manager_id,
                hp.cur_level,
                hp.contract_formal_date,
                hp.probation_channel_type,
                hp.status,
                hp.id as probation_id,
                hp.second_audit_status,
                hp.first_audit_status
            from
                  $dbName as hp
            left join
                hr_staff_info as hsi on hp.staff_info_id = hsi.staff_info_id
            where
                hsi.state!=2 and hsi.formal=1 and hsi.is_sub_staff = 0 and hp.hire_type = 2 and (hp.is_system=0 or hp.is_system is NULL) and hsi.hire_date= :hire_date and probation_channel_type=:probation_channel_type
        ";
        $bind = ['hire_date' => $hireDate,'probation_channel_type' => $type];
        return $this->db_rby->query(
            $sql,
            $bind
        )->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }
	/**
	 * 根据入职时间获得员工
	 * @param $hire_date_begin
	 * @param $hire_date_end
	 * @param int $is_delay 是否是延长试用期的用户
	 * @return array
	 */
	public function getStaffs($hire_date_begin='', $hire_date_end='',$is_delay=0,$greater_job_title_grade_v2='',$less_job_title_grade_v2='',$date_logic = false): array
    {
		$sql = "
            select
                hsi.staff_info_id,
                hsi.job_title_level,
                hsi.job_title_grade_v2,
                hsi.hire_date,
                hsi.manger as manager_id,
                hp.cur_level,
                hp.status,
                hp.id as probation_id,
                hp.second_audit_status,
                hp.first_audit_status
            from
                hr_staff_info as hsi
            left join
                hr_probation as hp on hp.staff_info_id = hsi.staff_info_id
            where
                hsi.state!=2 and hsi.formal=1 and hsi.is_sub_staff = 0 and hsi.hire_type != 13 and (hp.is_system=0 or hp.is_system is NULL)
        ";
        
        if (!isCountry('MY') && $date_logic){
            $hire_date_end = date("Y-m-d", strtotime("-6 month", strtotime($hire_date_begin)));
            $sql .= 'and hsi.hire_date <= :hire_date_begin and hsi.hire_date>=:hire_date_end';
        }else{
            // 之前老逻辑不动
            $sql .= 'and hsi.hire_date>= :hire_date_begin and hsi.hire_date< :hire_date_end';
        }

		if(empty($is_delay)){
			$sql.=" and (hp.is_delay=0 or hp.is_delay is NULL)";
		}else{
			$sql.=" and hp.is_delay=1";
		}
        $bind = ['hire_date_begin' => $hire_date_begin, 'hire_date_end' => $hire_date_end];
        //增加条件
        if (!empty($greater_job_title_grade_v2)) {
            $sql .= ' and hsi.job_title_grade_v2 > :begin_job_title_grade_v2 ';
            $bind['begin_job_title_grade_v2'] = (int)$greater_job_title_grade_v2;
        }

        //增加条件
        if (!empty($less_job_title_grade_v2)) {
            $sql .= ' and ifnull(hsi.job_title_grade_v2,0) <= :end_job_title_grade_v2 ';
            $bind['end_job_title_grade_v2'] = (int)$less_job_title_grade_v2;
        }
        if (isCountry('PH')) {
            $sql .= " and hsi.hire_type = 1 and (hp.hire_type is null or hp.hire_type=1)";
        } else {
            $sql .= " and ( hsi.hire_type = 1 or ( hsi.hire_type in (3,4) and hsi.hire_times >= 365) or ( hsi.hire_type in (2) and hsi.hire_times >= 12) ) ";
        }
		return $this->getDI()->get("db_rby")->query(
			$sql,
            $bind
		)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
	}

	/**
	 * 根据新的职级来获得模板
	 * @param $grade
	 * @return int
	 */
	public function getTplIdByJobTitleGradeV2($grade)
	{
		$grade = intval($grade);
		//小于等于14都是模板1
		if ($grade <= 14) {
			return 1;
		}
		//17及以上，都是3
		if ($grade >= 17) {
			return 3;
		}
		//其他情况2
		return 2;
	}



	/**
	 * 获得最新的审批日志
	 * @param $staff_info_id
	 * @return array
	 */
	public function getLastestProbationAudit($staff_info_id='')
	{
		$sql = "
                select
                    *
                from
                    hr_probation_audit
                where
                    staff_info_id = :id
                order by
                    id desc
            ";
		return $this->getDI()->get("db_rby")->fetchOne($sql, \PDO::FETCH_ASSOC, ["id" => $staff_info_id]);
	}

    public function getProbationAuditStaffIdsByStaffIds($staffIds): array
    {
        if (empty($staffIds)) {
            return [];
        }
        $list = HrProbationAuditModel::find([
            'columns'    => 'staff_info_id,probation_id',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind'       => ['ids' => $staffIds],
        ])->toArray();
        return array_column($list, 'probation_id', 'staff_info_id');
    }

	/**
	 * 根据截止时间，
	 * @param $start
	 * @param $end
	 * @return array
	 */

	public function getStaffsByDeadlineDate($start='', $end='')
	{
		$sql = "
            select
                hpa.*
            from
                hr_probation_audit as hpa
            left join
                hr_probation as hp on hp.id =hpa.probation_id
            where
                hpa.deadline_at>=:start and hpa.deadline_at<:end and hpa.audit_status=1 and hp.is_system=0
        ";
        if (isCountry('PH')) {
            $sql .= ' and hp.hire_type!=' . HrStaffInfoModel::HIRE_TYPE_2;
        }
		return $this->getDI()->get("db_rby")->query(
			$sql,
			["start" => $start, "end" => $end]
		)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
	}

	/**
	 *
	 * 根据截止时间找第二阶段快要超时的人
	 * @param $start
	 * @param $end
	 * @return array
	 */
	//@param int $audit_level   (后来，不修改主表，所以不需要了。)
	public function getSecondStaffsByDeadlineDate($start, $end,$oldProbation = true)
	{
        $probationName = $oldProbation ? 'hr_probation' : 'hr_probation_contract_worker';
        $probationAuditName = $oldProbation ? 'hr_probation_audit' : 'hr_probation_audit_contract_worker';
		//最后截止时间85天的
		$sql = "
            select
                hpa.*
            from
                $probationAuditName as hpa
            left join
                $probationName as hp on hp.id =hpa.probation_id
            where
                hpa.deadline_at>=:start and hpa.deadline_at<:end and hpa.cur_level=2 and hpa.audit_status=1 and hp.is_system=0
        ";
		$first = $this->getDI()->get("db_rby")->query(
			$sql,
			["start" => $start, "end" => $end]
		)->fetchAll(\Phalcon\Db::FETCH_ASSOC);



		//中间截止时间80的，老的数据没有这项为空
		$sql = "
            select
                hpa.*
            from
                $probationAuditName as hpa
            left join
                $probationName as hp on hp.id =hpa.probation_id
            where
                hpa.second_deadline_at>=:start and hpa.second_deadline_at<:end and hpa.cur_level=2 and hpa.audit_status=1 and hp.is_system=0
        ";
		$second = $this->getDI()->get("db_rby")->query(
			$sql,
			["start" => $start, "end" => $end]
		)->fetchAll(\Phalcon\Db::FETCH_ASSOC);



		return array_merge($first,$second);
		//return $this->_querySql($sql, "db_rbi", ["start" => $start, "end" => $end]);
	}

    /**
     * 给hrbp发by消息和push
     * @param $lang
     * @param $hrbpId
     * @param $staffIds
     */
    public function sendPushMessageToHrbp($lang, $hrbpId, $staffIds)
    {
        $t                           = $this->getTranslation($lang);
        $staffIds                    = array_values(array_unique($staffIds));
        $staffMess                   = $this->getStaffMessForSendHrbp($staffIds);
        $param['staff_users']        = [$hrbpId];
        $param['message_title']      = $t->_('hr_probation_reminder');
        $param['message_content']    = addslashes("<div style='font-size: 30px'>" . $t->_('hr_probation_reminder_to_hrbp_content',
                ['staff_mess' => $staffMess]) . "</div>");
        $param['staff_info_ids_str'] = $hrbpId;
        $param['id']                 = time() . $hrbpId . rand(1000000, 9999999);
        $param['category']           = -1;
        $bi_rpc                      = (new ApiClient('bi_rpc', '', 'add_kit_message', $lang));
        $bi_rpc->setParams($param);
        $res = $bi_rpc->execute();
        if ($res && $res['result']['code'] == 1) {
            $messageId = $res['result']['data'][0];
            $this->logger->write_log('send_msg_to_staffAction message_backyard  写入message成功' . $hrbpId . " message_id" . $messageId,
                'info');
            $data                    = [];
            $data['staff_info_id']   = $hrbpId;
            $data['src']             = 'backyard';
            $data['message_title']   = $param['message_title'];
            $data['message_content'] = $t->_('hr_probation_reminder_to_hrbp_content',
                ['staff_mess' => $staffMess]);
            $data['message_scheme']  = "flashbackyard://fe/page?path=message&messageid=" . $messageId;

            $this->logger->write_log('send_msg_to_hrbpAction send_msg_to_hrbpAction_pushMessage params:' . json_encode($data),
                'info');
            $ret = (new ApiClient('bi_rpc', '', 'push_to_staff'));
            $ret->setParams($data);
            $_data = $ret->execute();
            $this->logger->write_log("send_msg_to_hrbpAction send_msg_to_hrbpAction_pushMessage:pushMessage-return- " . json_encode($_data),
                'info');
            if (!$_data['result']) {
                $this->logger->write_log(" send_msg_to_hrbpAction 第二阶段的提醒HRBP send_msg_to_hrbpAction_pushMessage hrbp:staff_info_id=" . $hrbpId . "  发送push失败",
                    'info');
            }
        }
    }
    /**
     * 发送by 消息给hrbp的员工信息
     * @param $staffIds
     * @return string
     */
    public function getStaffMessForSendHrbp($staffIds): string
    {
        if (empty($staffIds)) {
            return '';
        }
        $find      = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind'       => ['ids' => $staffIds],
            'columns'    => 'staff_info_id,name',
        ])->toArray();
        $staffMess = '';
        foreach ($find as $item) {
            $staffMess .= $item['name'] . '(' . $item['staff_info_id'] . '),';
        }
        return trim($staffMess,',');
    }

	/**
	 * 根据审批人id，找对应的hrbp 这是员工转正评估用的
	 * @param $staffInfoIds
	 * @return array
	 */
	public function findHRBP($staffInfoIds=[]): array
    {
        if (empty($staffInfoIds)) {
            return [];
        }
		$arr = HrStaffInfoModel::find([
			                                     'conditions' => " staff_info_id in({staff_info_id:array}) and state=:state:",
			                                     'bind'       => [
				                                     'staff_info_id'=> array_values($staffInfoIds),
                                                     'state' => HrStaffInfoModel::STATE_1,
			                                     ],
			                                     'columns' => 'staff_info_id,node_department_id,sys_store_id',
		                                     ])->toArray();
		if (empty($arr)) {
			return [];
		}

		$hrbpMap = [];
		$WorkflowServer = new WorkflowServer($this->lang, $this->timeZone);
		foreach($arr as $item){
			$findHRBP = $WorkflowServer->findHRBP($item['node_department_id'], ["store_id" => $item['sys_store_id']]);
			if(!empty($findHRBP)){
			    foreach (explode(',', $findHRBP) as $hrbpId) {
                    $hrbpMap[$hrbpId][] = $item['staff_info_id'];
                }
			}

		}
		return $hrbpMap;
	}


	public function getStaffsByFormalDate($start, $end, $status = [ProbationServer::STATUS_PASS],$date_logic = false)
	{
		$sql = "
            select
                hsi.staff_info_id,
                hsi.name,
                hp.formal_at,
                hp.status,
                hp.second_audit_status,
                hp.second_status,
                hp.probation_channel_type,
                hp.second_stage_done_msg_id,
                hp.second_stage_done_manager_msg_id,
                hsi.sys_department_id,
                hsi.sys_store_id,
                hsi.node_department_id,
                hsi.job_title,
                hjt.job_name,
                sd.name as department_name,
                ss.name as store_name,
                hsi.manger
            from
               hr_probation as hp
            left join
                hr_staff_info as hsi on hsi.staff_info_id = hp.staff_info_id
            left join
                hr_job_title as hjt on hsi.job_title = hjt.id
            left join
                sys_department as sd on sd.id = hsi.node_department_id
            left join
                sys_store as ss on ss.id = hsi.sys_store_id
            where
               hp.status in (" . implode(",", $status) . ")
               and hp.is_system IN (0,2)
        ";
        $bind = ["start" => $start];
        if (
            !isCountry('MY') && $date_logic
        ){
            $date_end = date("Y-m-d", strtotime("-6 month", strtotime($start)));
            $sql .= 'and hp.formal_at <= :start and hp.formal_at>= :date_end';
            $bind = ["start" => $start,"date_end" => $date_end];
        }else{
            // 之前老逻辑不动
            $sql .= 'and hp.formal_at = :start';
        }
        
        if (isCountry('PH')) {
            $sql .= ' and hp.hire_type!='.HrStaffInfoModel::HIRE_TYPE_2;
        }
		return $this->getDI()->get("db_rby")->query($sql, $bind)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
	}


	public function formalStaffs($staffInfoIds)
	{
		$data = [];
        if (isCountry('TH')){
            $data['result_notification'] = HrProbationModel::RESULT_NOTIFICATION_YES;
        }
		$data['status'] = HrProbationModel::STATUS_FORMAL;
		$data['second_audit_status'] = HrProbationModel::SECOND_AUDIT_STATUS_DONE;
		$data['second_status'] = HrProbationModel::SECOND_STATUS_PASS;
		$data['updated_at'] = gmdate("Y-m-d H:i:s", time() + ($this->add_hour)*3600);
		return $this->getDI()->get("db")->updateAsDict("hr_probation", $data, ["conditions" => 'staff_info_id in (' . implode(",", $staffInfoIds) . ')']);
	}

    /**
     * 操作未通过试用期
     * @param $hrProbations
     */
    public function failStaffs($hrProbations)
    {
        foreach ($hrProbations as $hrProbation) {
            $updateData = [
                'status' => HrProbationModel::STATUS_NOT_PASS,
            ];
            if ($hrProbation['cur_level'] == HrProbationModel::CUR_LEVEL_FIRST) {
                $updateData['first_audit_status'] = HrProbationModel::FIRST_AUDIT_STATUS_DONE;
                $updateData['first_status']       = HrProbationModel::FIRST_STATUS_NOT_PASS;
            } else {
                $updateData['second_audit_status'] = HrProbationModel::SECOND_AUDIT_STATUS_DONE;
                $updateData['second_status']       = HrProbationModel::SECOND_STATUS_NOT_PASS;
            }
            $this->db->updateAsDict("hr_probation", $updateData, ['conditions' => 'id=' . $hrProbation['id']]);
        }
    }

    /**
     * 是否记录考核，因为能修改入职日期
     * @param $staff_info_id
     * @param $cur_level
     * @param $probationChannelType
     * @return bool
     */
	public function isHaveAttendance($staff_info_id, $cur_level,$probationChannelType = null): bool
    {
        $dbName = $probationChannelType == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3 ? 'hr_probation_attendance_contract_worker' : 'hr_probation_attendance';
		$sql = "
            select
                *
            from
                $dbName
            where
                staff_info_id=:id
                and cur_level =:level
        ";
		$item = $this->getDI()->get("db_rby")->fetchOne($sql, \PDO::FETCH_ASSOC, ["id" => $staff_info_id, "level" => $cur_level]);
		if (empty($item)) {
			return false;
		}
		return true;
	}


	public function addAttendance($data)
	{

		//请假类型
		//1:年假   （单独规则）
		//2:带薪事假  7天  （单独规则）
		//3:病假  30天
		//4:产假  90天 1次
		//5:陪产假 7天 1次 （单独规则）
		//6:国家军训假  60天 1次
		//7:家人去世假 7天 （单独规则）
		//8:绝育手术假 1次
		//9:个人受训假 30天 一年一次
		//10:婚假  3天 1次 （单独规则）
		//11:出家假  7天  1次
		//12:不带薪事假
		//13:调休 （已废弃）
		//15:休息日 （单独规则）
		//16:公司培训假 8天
		//14:其他（已废弃）
		//17:产检 8天
		//18:无薪病假 60天

		$data['late'] = $this->getLateMinute($data['staff_info_id']);
		$data['sick'] = $this->getDaysByType($data['staff_info_id'], 18) + $this->getDaysByType($data['staff_info_id'], 3);      //不带薪病假
		$data['casual'] = $this->getDaysByType($data['staff_info_id'], 12) + $this->getDaysByType($data['staff_info_id'], 2);    //不带薪事假
		$data['lack'] = $this->getLackNum($data['staff_info_id']);

		return $this->getDI()->get("db")->insertAsDict("hr_probation_attendance", $data);
	}

    /**
     * 获得迟到分钟数
     * @param $staff_info_id
     * @param null $startDate
     * @return integer
     */

	public function getLateMinute($staff_info_id,$startDate = null)
	{
		$sql = "
            select
               punish_category,
               abnormal_time,
               extra_info
            from
               abnormal_message force index(idx_staff_create)
            where
               staff_info_id =:id and punish_category=12 and isdel=0 and state=1
        ";
		$arr = $this->getDI()->get("db_rbi")->query($sql, ["id" => $staff_info_id])->fetchAll(\Phalcon\Db::FETCH_ASSOC);

		$minute = 0;
		foreach ($arr as $k => $v) {
			$extract = json_decode($v['extra_info'], 1);
			$minute += isset($extract['abnormal_minute']) ? $extract['abnormal_minute'] : 0;
		}
		return $minute;
	}


    /**
     * 获取请假天数
     * @param $staff_info_id
     * @param $type
     * @param null $startDate
     * @return mixed
     */

    public function getDaysByType($staff_info_id, $type, $startDate = null)
    {
        $now = date('Y-m-d');
        $sql = "SELECT sum(IF(salp.type=0,1,0.5)) as num
            from staff_audit as  sa
            left join staff_audit_leave_split as salp on sa.audit_id = salp.audit_id
            where sa.staff_info_id=:id and   sa.audit_type = 2 and sa.status=2 and sa.leave_type=:type and salp.date_at<'$now'";
        if ($startDate) {
            $sql .= " and salp.date_at>='$startDate'";
        }
        $item = $this->getDI()->get("db")->fetchOne($sql, \PDO::FETCH_ASSOC, ["id" => $staff_info_id, "type" => $type]);
        if (empty($item['num'])) {
            return 0;
        }
        return $item['num'];
    }


    /**
     * 获得缺勤天数
     * @param $staff_info_id
     * @param null $startDate
     * @return integer
     */
	public function getLackNum($staff_info_id,$startDate = null)
	{
        $now = date('Y-m-d');
		$sql = "
            select sum(AB) as num from attendance_data_v2 where staff_info_id=:id and attendance_time=0 and is_trial=0 and stat_date<'$now'
        ";
		$item = $this->getDI()->get("db_rby")->fetchOne($sql, \PDO::FETCH_ASSOC, ["id" => $staff_info_id]);
		if (empty($item['num'])) {
			return 0;
		}
		return $item['num'];
	}



	/**
	 * 根据第二阶段截止时间判断，原来75-80改成75-85，中间存一个新的80天
	 * @param $start
	 * @param $end
	 * @return array
	 */
	public function getSecondStaffsBySecondDeadlineDate($start,$end){

		//找当前人的名字，审批人的名字，审批人的上级

		$sql = "
                SELECT
                    hsi.`name`,
                    hsi.`staff_info_id`,
                    man.`name` as manager_name,
                    man.`staff_info_id` as manager_id,
                    item.`value` as higher_id
                FROM
                    hr_probation_audit AS hpa
                    LEFT JOIN hr_probation AS hp ON hp.id = hpa.probation_id
                    LEFT JOIN hr_staff_info AS hsi ON hsi.staff_info_id = hpa.staff_info_id
                    LEFT JOIN hr_staff_items AS item ON item.staff_info_id = hpa.audit_id AND item.item = 'MANGER'
                    LEFT JOIN hr_staff_info AS man ON man.staff_info_id = hpa.audit_id
                WHERE
                    hpa.second_deadline_at >= :start
                    AND hpa.second_deadline_at < :end
                    AND hpa.cur_level = 2
                    AND hpa.audit_status = 1
                    AND hp.is_system = 0";
		return $this->getDI()->get("db_rby")->query($sql,["start" => $start, "end" => $end])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
//		return $this->_querySql($sql, "db_rbi", ["start" => $start, "end" => $end]);
	}


	/**
	 * @description:获取待处理的条数
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/9/16 19:59
	 */
	public function getCountAuditStatus($audit_id='')
    {
        if (empty($audit_id)) {
            return 0;
        }

		//开始时间=截止日期比当前时间多1天
		//结束时间=截止日期比当期时间多2天（比开始时间多一天）
        // 被评估人非离职 超时和待评估 
		$start = gmdate("Y-m-d 00:00:00", time() + ($this->add_hour) * 3600);
        // 上线时间
        $created_at = '2024-12-18 16:00:00';
		$start = $this->getDateByDays($start, 1, 1);//大于等于
		$end   = $this->getDateByDays($start, 1, 1);//<
		$sql = "
            select
                count(1) as num
            from
                hr_probation_audit as hpa
            left join
                hr_probation as hp on hp.id =hpa.probation_id
            left join hr_staff_info as s on s.staff_info_id = hp.staff_info_id
            where
                 hpa.audit_id = :audit_id and hp.is_system=0
        ";
        if (isCountry('PH')){
            $sql .= ' and s.state != 2 and hpa.deadline_at<=:start and hp.status !=4 and ((hpa.audit_status =3 and hpa.is_active != 1 and hpa.created_at >= :created_at) or hpa.audit_status =1)';
            $data = ["audit_id" => (int)$audit_id,"start" => $start,"created_at" => $created_at];
        }else{
            $sql .= ' and hpa.deadline_at>=:start and hpa.deadline_at<:end and hpa.audit_status=1';
            $data = ["audit_id" => (int)$audit_id,"start" => $start, "end" => $end];
        }
		$arr   = $this->getDI()->get("db_rby")->fetchOne($sql, \PDO::FETCH_ASSOC, $data);
        //合同工五个月
        $contractNum  = $this->getCountAuditStatusFromProbationContractT($audit_id, $start, $created_at);
        
		return $arr['num'] + $contractNum;
	}

    /**
     * 获取分出去的合同工五个月数据
     * @param $audit_id
     * @param $start
     * @param $created_at
     * @return mixed
     */
    public function getCountAuditStatusFromProbationContractT($audit_id, $start, $created_at)
    {
        if (!isCountry('PH')) {
            return 0;
        }
        $sql  = "
            select
                count(1) as num
            from
                hr_probation_audit_contract_worker as hpa
            left join
                hr_probation_contract_worker as hp on hp.id =hpa.probation_id
            left join hr_staff_info as s on s.staff_info_id = hp.staff_info_id
            where
                 hpa.audit_id = :audit_id and hp.is_system=0
        ";
        $sql  .= ' and s.state != 2 and hpa.deadline_at<=:start and hp.status !=4 and ((hpa.audit_status =3 and hpa.is_active != 1 and hpa.created_at >= :created_at) or hpa.audit_status =1)';
        $data = ["audit_id" => (int)$audit_id, "start" => $start, "created_at" => $created_at];
        $arr  = $this->getDI()->get("db_rby")->fetchOne($sql, \PDO::FETCH_ASSOC, $data);
        return $arr['num'];
    }

	/**
	 * @description:  把签字消息 保存下来
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/12/2 14:55
	 */

	public function  saveSign($probation_audit_id,$sign_url){
		if(empty($probation_audit_id) || empty($sign_url)){
			return false;
		}
		$res = $this->getDI()->get('db')->updateAsDict("hr_probation_message", [
			"sign_url" => $sign_url,
			'updated_at' => gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
			'sign_status'=>2,
			'sign_time'=>gmdate("Y-m-d H:i:s", time() + ( $this->add_hour)*3600),
		],  [
			"conditions" => " probation_audit_id = ? ",
			"bind" => [
				$probation_audit_id,
			],
		]);
		return  $res;

	}


    /**
     * @description: 查询只取在职和停职的员工已超时 状态的评估
     * @param $start 开始时间
     * @param $end 结束时间
     * @param $cur_level   阶段
     * @param $audit_level   1 上级 2 上上级  0 没有
     * @param $audit_status   状态 默认超时
     * @param $is_get_active     默认查询激活数据
     * @param $is_get_positive    默认查询包含试用期状态的数据,  true 查询 状态 为 1(试用期中) 的数据
     * @return     : array
     * <AUTHOR> L.J
     * @time       : 2022/4/13 15:05
     */

    public function getStaffsByDeadlineDateOnTheJob($start='', $end='', $cur_level = self::CUR_LEVEL_FIRST , $audit_level = 0,$audit_status = [self::AUDIT_STATUS_TIMEOUT],$is_get_active = false,$is_get_positive = false)
    {
        $conditions = " hsi.state in (1,3) and wait_leave_state = 0   and hpa.cur_level = :cur_level: and hp.is_system=0 ";
        $bina['cur_level'] = $cur_level; //阶段
        //状态
        if($audit_status){
            $conditions .= ' and hpa.audit_status in ({audit_status:array})';
            $bina['audit_status'] = $audit_status;
        }
        //这是第几次审批
        if($audit_level){
            $conditions .= ' and hpa.audit_level = :audit_level: ';
            $bina['audit_level'] = $audit_level;
        }
        //这是截止日期
        if($start){
            $conditions .= '  and hpa.deadline_at >= :start: ';
            $bina['start'] = $start;
        }
        //这是截止日期
        if($end){
            $conditions .= '   and hpa.deadline_at < :end: ';
            $bina['end'] = $end;
        }

        //不查询激活的数据
        if($is_get_active){
            $conditions .= '   and hp.is_active = 0 ';
        }
        //查询 hp.status = 1  状态在试用期中,没有进行过第二阶段评估操作的数据
        if($is_get_positive){
            $conditions .= '  and hp.status = :hp_status: ';
            $bina['hp_status'] = self::STATUS_PROBATION;
        }
        if (isCountry('PH')) {
            $conditions .= '  and hp.hire_type != :hire_type: ';
            $bina['hire_type'] = HrStaffInfoModel::HIRE_TYPE_2;
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.name, hpa.staff_info_id,hsi.node_department_id,hsi.job_title,hsi.sys_store_id,hpa.cur_level,hpa.audit_id,hpa.deadline_at,hpa.audit_level,hpa.status');
        $builder->from(['hpa' => HrProbationAuditModel::class]);
        $builder->leftjoin(HrProbationModel::class, 'hp.id=hpa.probation_id', 'hp');
        $builder->leftjoin(HrStaffInfoModel::class, 'hsi.staff_info_id = hpa.staff_info_id', 'hsi');
        $builder->where($conditions,$bina);
        $builder->orderBy('hpa.status desc,hpa.audit_level desc');
        $arr = $builder->getQuery()->execute()->toArray();

        //获取部门
        $node_department_ids =  array_values(array_unique(array_column($arr, 'node_department_id')));
        $node_department_id_arr = [];
        if($node_department_ids){
            $node_department_id_arr = SysDepartmentModel::find(['conditions' => 'id in  ({ids:array}) ',
                                                    'bind'       => ['ids' => $node_department_ids],
                                                    'columns'    => 'id,name,ancestry_v3',
                                                   ])->toArray();
            $node_department_id_arr = array_column($node_department_id_arr, null, 'id');
        }

        //获取职位
        $job_title_ids = array_values(array_unique(array_column($arr, 'job_title')));
        $job_title_arr = [];
        if($job_title_ids) {
            $job_title_arr = HrJobTitleModel::find(['conditions' => 'id in  ({ids:array}) ',
                                                    'bind'       => ['ids' => $job_title_ids],
                                                    'columns'    => 'id,job_name',
                                                   ])->toArray();
            $job_title_arr = array_column($job_title_arr, 'job_name', 'id');
        }
        //获取审批人姓名
        $audit_ids =  array_values(array_unique(array_column($arr, 'audit_id')));
        $staff_arr = [];
        if($audit_ids) {
            $staff_arr = HrStaffInfoModel::find([
                                                    'conditions' => 'staff_info_id in ({staffs:array}) ',
                                                    'bind'       => ['staffs' => $audit_ids],
                                                    'columns'    => ['staff_info_id', 'name'],
                                                ])->toArray();
            $staff_arr = array_column($staff_arr, 'name', 'staff_info_id');
        }
        //获取网点
        $sys_store_ids = array_values(array_unique(array_column($arr, 'sys_store_id')));
        $sys_store_arr = [];
        if($sys_store_ids) {
            $sys_store_arr = SysStoreModel::find([
                                                     'conditions' => 'id in ({ids:array}) ',
                                                     'bind'       => ['ids' => $sys_store_ids],
                                                     'columns'    => "id,name",
                                                 ])->toArray();
            $sys_store_arr = array_column($sys_store_arr, 'name', 'id');
        }
        $sys_store_arr['-1'] = "Head Office";
        foreach($arr as $k=>$v){
            $arr[$k]['job_title_name'] = $job_title_arr[$v['job_title']] ?? ''; //评估人职位
            $arr[$k]['sys_store_name'] = $sys_store_arr[$v['sys_store_id']] ?? ''; //评估人所在网点
            $arr[$k]['audit_id_name'] = $staff_arr[$v['audit_id']] ?? ''; //取当前评估人姓名
            $arr[$k]['node_department_name'] = $node_department_id_arr[$v['node_department_id']]['name'] ?? ''; //获取部门
            $arr[$k]['ancestry_v3'] = $node_department_id_arr[$v['node_department_id']]['ancestry_v3'] ?? ''; //获取部门链
        }
        return $arr;
    }


    /**
     * @description: 获取转正评估配置项
     * @return     :[]
     * <AUTHOR> L.J
     * @time       : 2022/8/25 21:36
     */
    public function getProbationEvaluateEnv()
    {
        $setting_env = new SettingEnvServer();
        $probation_evaluate_env = $setting_env->getSetValFromCache('probation_evaluate_env');
        return (empty($probation_evaluate_env) ? [] : json_decode($probation_evaluate_env, true));
    }

    /**
     * 获取签了终止试用ACK的人员
     * @param $date
     * @return mixed
     */
    public function getStaffSigned($date)
    {
        $startTime = date('Y-m-d 00:00:00',strtotime($date." - 1 day"));
        $endTime = date('Y-m-d 00:00:00',strtotime($date));
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['hp' => HrProbationModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, " hsi.staff_info_id = hp.staff_info_id", 'hsi');
        $builder->columns(['hp.*']);
        $bindParams = [
            'start_time' => $startTime,
            'end_time' => $endTime,
        ];
        $builder->where(" hp.sign_time >= :start_time: and hp.sign_time < :end_time: and hp.is_delay=0 ",$bindParams );
        if (isCountry('PH')) {
            $builder->andWhere('hp.hire_type !=:hire_type:',['hire_type' => HrStaffInfoModel::HIRE_TYPE_2]);
        }
        $data = $builder->getQuery()->execute()->toArray();

        return $data;
    }

    /**
     * 获取到转正日期虽然未通过但是继续留用的人员
     * @param $date
     * @return mixed
     */
    public function getStaffsNotTerminated($date)
    {
        //17级及以下数据
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['hp' => HrProbationModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, " hsi.staff_info_id = hp.staff_info_id", 'hsi');
        $builder->columns(['hp.*']);
        $builder->where("
        status = 3 and hp.cur_level = 2 AND formal_at = :date: and hsi.job_title_grade_v2 <= :grade:
        and EXISTS (
            SELECT
              1
            FROM
              FlashExpress\bi\App\Models\backyard\HrProbationAuditModel as hpa
            WHERE
              hpa.probation_id = hp.id
              AND hpa.cur_level = hp.cur_level
              AND hpa.audit_level = 2
              and hpa.is_terminate = 2
          )
        ",['date'=>$date,'grade'=> $this->job_grade_exceed]);
        $data1 = $builder->getQuery()->execute()->toArray();
        //17级以上数据
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['hp' => HrProbationModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, " hsi.staff_info_id = hp.staff_info_id", 'hsi');
        $builder->columns(['hp.*']);
        $builder->where("
        status = 3 and hp.cur_level = 2 AND formal_at = :date: and hsi.job_title_grade_v2 > :grade:
        and EXISTS (
            SELECT
              1
            FROM
              FlashExpress\bi\App\Models\backyard\HrProbationAuditModel as hpa
            WHERE
              hpa.probation_id = hp.id
              AND hpa.cur_level = hp.cur_level
              AND hpa.audit_id = :cpo:
              and hpa.is_terminate = 2
          )
        ",['date'=>$date,'grade'=> $this->job_grade_exceed,'cpo'=> $this->cpo_staff_id]);
        $data2 = $builder->getQuery()->execute()->toArray();

        return $data1 + $data2;
    }


    /**
     * @param $staffInfo
     * @return false|string
     */
    public function getLeaveDateForAck($staffInfo)
    {
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $jobs = $this->getCountryJobs();

        if (in_array($staffInfo['job_title'], $jobs)) {
            $leaveDate = gmdate("Y-m-d", time() + $add_hour * 3600 + 86400);
        } else{
            $leaveDate = gmdate("Y-m-d", time() + $add_hour * 3600 + 14 * 86400);
        }

        return $leaveDate;
    }

    /**
     * 终止试用次日需离职职位
     * @return array
     */
    public function getCountryJobs()
    {
        return [enums::$job_title['bike_courier'], enums::$job_title['van_courier'], enums::$job_title['tricycle_courier']];
    }

    /**
     *
     * @param $staffInfoId
     * @return void
     */
    protected function holdStaff($staffInfoId)
    {
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $bi_params = [
            'staff_info_id' => $staffInfoId,//员工id
            'hold_reason'   => 'incomplete_resignation_procedures3',//hold原因(BY申请离职)
            'hold_remark'   => '',//hold备注
            'hold_time'     => gmdate('Y-m-d H:i:s', time() + $add_hour * 3600),//hold时间操作时间
            'hold_source'   => 9,//来源-> 试用期没有通过
            'type'          => '1,2',// hold 提成 工资
        ];
        $bi_rpc = (new ApiClient('hcm_rpc', '', 'synchronize_hold'));
        $bi_rpc->setParams($bi_params);
        $bi_return = $bi_rpc->execute();
        $this->getDI()->get('logger')->write_log('signProbation add hold :param:' . json_encode($bi_params, JSON_UNESCAPED_UNICODE) . " " . json_encode($bi_return, JSON_UNESCAPED_UNICODE), 'info');
    }

    /**
     * 变更员工状态
     * @param $staffInfoId
     * @return void
     */
    public function syncStaffState($staffInfoId)
    {
    }

    /**
     * 发送终止试用消息
     * @param $staffInfo
     * @param $audit
     * @return void
     */
    protected function sendTerminateMsg($staffInfo, $audit)
    {
        $src = env('sign_url') . "/#/followUpEmployees?id=" . $audit['id'] . "&probation_id=" . $audit['probation_id'] . "&cul_level=" . $audit['cul_level'] . "&manger_id=" . $staffInfo['manger'];
        $html = "<meta name=\"viewport\" content=\"width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no\" /><iframe src='{$src}' width='100%' height='85%'></iframe>";

        $param['staff_users'] = [$staffInfo['manger']];//数组 多个员工id 22 22
        $param['message_title'] = $this->getMsgTemplateByUserId($staffInfo['manger'], 'probation_terminate_notice');
        $param['message_content'] = $html;
        $param['staff_info_ids_str'] = $staffInfo['manger'];
        $param['id'] = time() . $staffInfo['manger'] . rand(1000000, 9999999);
        $param['category'] = MessageEnums::MESSAGE_CATEGORY_70;//消息为 70 跟进试用期未通过员工
        $param['related_id'] = strtotime(' +2 day midnight');
        $this->getDI()->get('logger')->write_log('send_msg_on_terminate :param:' . json_encode($param), 'info');
        $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang));
        $bi_rpc->setParams($param);
        $res = $bi_rpc->execute();
        $this->getDI()->get('logger')->write_log('send_msg_on_terminate -result:' . json_encode($res), 'info');
        if ($res && $res['result']['code'] == 1) {
            $kitId = $res['result']['data'][0];
            $this->db->updateAsDict("hr_probation_audit", [
                'message_id'      => $kitId,
                'follow_staff_id' => $staffInfo['manger'],
            ], ["conditions" => "id=" . intval($audit['id'])]);
        }

    }

    /**
     * 获取申请详情
     * @param $param
     * @return array
     */
    public function probationResignDetail($param)
    {
        $msgId = $param['msg_id'] ?? '';

        $resignInfo = HrProbationResignModel::findFirst([
            'conditions' => "msg_id = :msg_id: AND is_deleted = :is_deleted:",
            'bind'       => [
                'msg_id'     => $msgId,
                'is_deleted' => CommonEnums::IS_DELETED_NO,
            ],
            'columns'    => 'staff_info_id,notice_url',
        ]);

        return $resignInfo ? $resignInfo->toArray() : [];
    }

    /**
     * 查找已经存在的staff
     * @param $staffIds
     * @return array
     */
    public function getExistHrProbationByStaffIds($staffIds): array
    {
        if (empty($staffIds)) {
            return [];
        }
        $find = HrProbationModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind'       => ['ids' => $staffIds],
        ])->toArray();
        return array_column($find, 'staff_info_id');
    }

    /**
     * 转正评估-目标制定入职前提醒
     * @return bool
     */
    public function probationGoalRemind(): bool
    {
        if (!isCountry('TH')) {
            return false;
        }
        // 前7天
        $first_seven_day = date('Y-m-d 00:00:00', strtotime("+7 day"));
        // 入职后第7天
        $last_seven_day = date('Y-m-d 00:00:00', strtotime("-6 day"));
        // 入职后第5天
        $last_five_day = date('Y-m-d 00:00:00', strtotime("-4 day"));
        $today         = date('Y-m-d 00:00:00');
        // 转正白名单
        $probation_staff = (new SettingEnvServer())->getSetValToArray('probation_staff');

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('entry.staff_id, entry.resume_id,resume.name,offer.work_time,interview.manager_id,hc.department_id,hc.worknode_id,hc.job_title,resume.working_country');
        $builder->from(['entry' => HrEntryModel::class]);
        $builder->leftJoin(HrResumeModel::class, 'entry.resume_id=resume.id', 'resume');
        $builder->leftJoin(HrInterviewModel::class, 'entry.interview_id=interview.interview_id', 'interview');
        $builder->leftJoin(HrInterviewOfferModel::class, 'entry.interview_offer_id=offer.id', 'offer');
        $builder->leftJoin(HrHcModel::class, 'entry.hc_id=hc.hc_id', 'hc');
        $builder->where('entry.deleted = ' . HrResumeModel::IS_DELETED_NO . ' and entry.status != ' . HrEntryModel::STATUS_NOT_EMPLOYED);
        $builder->andWhere("hc.hire_type = :hire_type: and offer.work_time in ({work_time:array})",
            [
                'hire_type' => HrStaffInfoModel::HIRE_TYPE_1,
                'work_time' => [
                    $first_seven_day,
                    $last_seven_day,
                    $last_five_day,
                    $today,
                ],
            ]);
        $data      = $builder->getQuery()->execute()->toArray();
        $staff_ids = array_values(array_filter(array_column($data, 'staff_id', 'staff_id')));
        $staff = [];
        $target_data = [];
        if (!empty($staff_ids)){
            $staff     = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in ({staffs:array}) and state != :state:',
                'bind'       => [
                    'staffs'    => $staff_ids,
                    'state'     => HrStaffInfoModel::STATE_2,
                ],
            ])->toArray();
            $staff     = array_column($staff, null, 'staff_info_id');

            $target_data = HrProbationTargetModel::find([
                'conditions' => 'staff_info_id in ({staff_info_id:array}) and setting_state in ({setting_state:array}) and is_deleted = :is_deleted:',
                'bind'       => [
                    'staff_info_id'      => $staff_ids,
                    'setting_state' => [HrProbationTargetModel::SETTING_STATE_FINISH,HrProbationTargetModel::SETTING_STATE_ADJUST],
                    'is_deleted'    => 0,
                ],
            ])->toArray();
            $target_data     = array_column($target_data, null, 'staff_info_id');
        }
        $hrBpArr    = [];
        $managerArr = [];
        $staffServer = new StaffServer();
        $workflowServer = new WorkflowServer($this->lang, $this->timeZone);
        foreach ($data as $v) {
            if (!empty($v['staff_id']) && in_array($v['staff_id'], $probation_staff)) {
                $this->getDI()->get('logger')->write_log('probationGoalRemind 跳过，因在转正白名单里，不属于转正评估非一线员工 staff_id:' . $v['staff_id'],
                    'info');
                continue;
            }
            if (!empty($v['staff_id']) && !empty($target_data[$v['staff_id']])) {
                $this->getDI()->get('logger')->write_log('probationGoalRemind 已制定目标，跳过， staff_id:' . $v['staff_id'],
                    'info');
                continue;
            }
            if (!empty($v['staff_id'])) {
                if (empty($staff[$v['staff_id']])){
                    $this->getDI()->get('logger')->write_log('probationGoalRemind 该员工已离职，跳过， staff_id:' . $v['staff_id'],
                        'info');
                    continue;
                }
                $manager_id      = $staff[$v['staff_id']]['manger'] ?? '';
                $hire_date       = !empty($staff[$v['staff_id']]['hire_date']) ? date('Y-m-d',
                    strtotime($staff[$v['staff_id']]['hire_date'])) : (!empty($v['work_time']) ? date('Y-m-d',
                    strtotime($v['work_time'])) : '');
                $name            = !empty($staff[$v['staff_id']]['name']) ? $staff[$v['staff_id']]['name'] : ($v['name'] ?? '');
            } else {
                $manager_id      = $v['manager_id'] ?? '';
                $hire_date       = !empty($v['work_time']) ? date('Y-m-d', strtotime($v['work_time'])) : '';
                $name            = $v['name'] ?? '';
            }
            // 这几个在入职生成工号的时候 会根据这几个固化是否算非一线
            $_department_id  = $v['department_id'] ?? '';
            $_store_id       = $v['worknode_id'] ?? '';
            $_job_title      = $v['job_title'] ?? '';
            $working_country = $v['working_country'] ?? '';
            
            if (empty($working_country) || !in_array($working_country,[1,8])){
                $this->getDI()->get('logger')->write_log('probationGoalRemind 有错误数据 工作所在国家非泰国和新加坡 resume_id:' . ($v['resume_id'] ?? ''),
                    'info');
                continue;
            }
            if (empty($_department_id) || empty($_job_title)) {
                $this->getDI()->get('logger')->write_log('probationGoalRemind 有错误数据 部门或者职位为空 resume_id:' . ($v['resume_id'] ?? ''),
                    'info');
                continue;
            }
            $job_title_r = HrJobDepartmentRelationModel::findFirst([
                'conditions' => 'department_id = :department_id: and job_id = :job_id: and position_type in ({position_type:array})',
                'bind'       => [
                    'department_id' => $_department_id,
                    'job_id'        => $_job_title,
                    'position_type' => [
                        HrJobDepartmentRelationModel::POSITION_TYPE_2,
                        HrJobDepartmentRelationModel::POSITION_TYPE_3,
                    ],
                ],
            ]);
            if (empty($job_title_r)) {
                $this->getDI()->get('logger')->write_log('probationGoalRemind 部门职位不属于总部职能或者一线职能 resume_id:' . ($v['resume_id'] ?? '') . ' department_id:' . $_department_id . ',job_title:' . $_job_title,
                    'info');
                continue;
            }

            $hrBp         = $workflowServer->findHRBP($_department_id,
                ["store_id" => $_store_id]);
            if (empty($v['staff_id'])){
                $entry_staff_key = 'entry_before';
            }else{
                $entry_staff_key = 'entry_after';
            }
            $remind_staff = [
                'name'      => $name,
                'work_time' => $hire_date,
            ];
            if (!empty($manager_id)) {
                $manager_data = HrStaffInfoModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_info_id: and state = :state:',
                    'bind'       => [
                        'staff_info_id' => $manager_id,
                        'state'         => HrStaffInfoModel::STATE_1,
                    ],
                ]);
                if (!empty($manager_data)) {
                    $managerArr[$manager_id][$entry_staff_key][] = $remind_staff;
                }
            }
            if (!empty($hrBp)) {
                foreach (explode(',', $hrBp) as $vv) {
                    if (!empty($manager_id) && $vv == $manager_id) {
                        continue;
                    }
                    $hrBpArr[$vv][$entry_staff_key][] = $remind_staff;
                }
            }
        }
        $this->getDI()->get('logger')->write_log('probationGoalRemind hrBpArr :' . json_encode($hrBpArr), 'info');
        foreach ($hrBpArr as $k => $v) {
            $lang      = $staffServer->getLanguage($k);
            $t         = $this->getTranslation($lang);
            $entry_before_staff_arr = [];
            if (!empty($v['entry_before'])){
                foreach ($v['entry_before'] as $data) {
                    $entry_before_staff_arr[] = $data['name'] . $t->_('probation_goal_remind_would') . $data['work_time'] . $t->_('6102');
                }
            }
            if ($entry_before_staff_arr) {
                $msg_title   = $t->_('probation_goal_remind_message_title_entry_before');
                $msg_content = $t->_('probation_goal_remind_message_content_entry_before',
                    ['num' => count($entry_before_staff_arr), 'staff_data' => implode(',<br/>',$entry_before_staff_arr)]);
                $this->sendRemindMessage($k, $msg_title, $msg_content);
            }

            $entry_after_staff_arr = [];
            if (!empty($v['entry_after'])){
                foreach ($v['entry_after'] as $data) {
                    $entry_after_staff_arr[] = $data['name'] . $t->_('4604') . $data['work_time'] . $t->_('6102');
                }
            }
            if ($entry_after_staff_arr) {
                $msg_title   = $t->_('probation_goal_remind_message_title');
                $msg_content = $t->_('probation_goal_remind_message_content',
                    ['num' => count($entry_after_staff_arr), 'staff_data' => implode(',<br/>',$entry_after_staff_arr)]);
                $this->sendRemindMessage($k, $msg_title, $msg_content);
            }
        }
        $this->getDI()->get('logger')->write_log('probationGoalRemind managerArr :' . json_encode($managerArr), 'info');
        foreach ($managerArr as $k => $v) {
            $lang      = $staffServer->getLanguage($k);
            $t         = $this->getTranslation($lang);
            $entry_before_staff_arr = [];
            if (!empty($v['entry_before'])){
                foreach ($v['entry_before'] as $data) {
                    $entry_before_staff_arr[]= $data['name'] . $t->_('probation_goal_remind_would') . $data['work_time'] . $t->_('6102');
                }
            }
            if ($entry_before_staff_arr) {
                $msg_title   = $t->_('probation_goal_remind_message_title_entry_before');
                $msg_content = $t->_('probation_goal_remind_message_content_entry_before',
                    ['num' => count($entry_before_staff_arr), 'staff_data' => implode(',<br/>',$entry_before_staff_arr)]);
                $this->sendRemindMessage($k, $msg_title, $msg_content);
            }

            $entry_after_staff_arr = [];
            if (!empty($v['entry_after'])){
                foreach ($v['entry_after'] as $data) {
                    $entry_after_staff_arr[] = $data['name'] . $t->_('4604') . $data['work_time'] . $t->_('6102');
                }
            }
            if ($entry_after_staff_arr) {
                $msg_title   = $t->_('probation_goal_remind_message_title');
                $msg_content = $t->_('probation_goal_remind_message_content',
                    ['num' => count($entry_after_staff_arr), 'staff_data' => implode(',<br/>',$entry_after_staff_arr)]);
                $this->sendRemindMessage($k, $msg_title, $msg_content);
            }
        }
        return true;
    }

    /**
     * 发送消息
     * @param $staff_info_id
     * @param $msg_title
     * @param $msg_content
     * @return bool
     */
    public function sendRemindMessage($staff_info_id, $msg_title, $msg_content): bool
    {
        $this->getDI()->get('logger')->write_log("ProbationServer sendRemindMessage 发送消息开始 staff_info_id :" . $staff_info_id.'msg_content:'.$msg_content,'info');
        $msg_data = [
            "id"                 => time() . $staff_info_id . rand(1000000, 9999999),
            "staff_users"        => [$staff_info_id],
            "message_title"      => $msg_title,
            "message_content"    => addslashes("<div style='font-size: 30px'>" . $msg_content . "</div>"),
            "staff_info_ids_str" => $staff_info_id,
            "category"           => MessageEnums::MESSAGE_CATEGORY_SYS,
        ];
        $bi_rpc   = (new ApiClient('hcm_rpc', '', 'add_kit_message', $this->lang));
        $bi_rpc->setParams($msg_data);
        $res = $bi_rpc->execute();
        if ($res && $res['result']['code'] == 1) {
            $msg_id = $res['result']['data'][0];
            $this->getDI()->get('logger')->write_log("ProbationServer sendRemindMessage 发送消息成功 : staff_info_id:{$staff_info_id} ,msg_id:{$msg_id},msg_data : " . json_encode($res),
                'info');
        } else {
            $this->getDI()->get('logger')->write_log("ProbationServer sendRemindMessage 发送消息失败 : staff_info_id:{$staff_info_id} ,msg_data:" . json_encode($msg_data) . ',消息返回 : ' . json_encode($res),
                'error');
            return false;
        }
        return true;
    }

    /**
     * 试用期未通过的员工通知 -结果通知
     * @param $param
     * @return array
     * @throws ValidationException
     */
    public function notPassNotice($param): array
    {
        if (empty($param['staff_info_id'])) {
            throw new ValidationException($this->t->_('miss_args'));
        }
        $staff_data = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $param['staff_info_id'],
            ],
        ]);
        if (empty($staff_data)) {
            throw new ValidationException($this->t->_('data_error'));
        }
        $probation_data = HrProbationModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $param['staff_info_id'],
            ],
        ]);
        $probation_data = !empty($probation_data) ? $probation_data->toArray() : [];
        if (empty($probation_data) || $probation_data['status'] != HrProbationModel::STATUS_NOT_PASS) {
            throw new ValidationException($this->t->_('data_error'));
        }

        $staff_data  = $staff_data->toArray();
        $msg_content = $this->getMsgTemplateByUserId($param['staff_info_id'],
            "hr_probation_field_msg_to_staff_not_pass", ['name' => $staff_data['name']]);
        $msg_title   = $this->getMsgTemplateByUserId($param['staff_info_id'], 'hr_probation_field_msg_to_staff_title');
        $re          = $this->sendRemindMessage($param['staff_info_id'], $msg_title, $msg_content);
        if (!$re) {
            throw new ValidationException($this->t->_('4701'));
        }
        $this->getDI()->get('db')->updateAsDict('hr_probation',
            ['result_notification' => HrProbationModel::RESULT_NOTIFICATION_YES], 'id = ' . $probation_data['id']);
        return $this->checkReturn(1);
    }

    /**
     * 是否是 转正评估非一线员工
     * （转正白名单后续添加 会变更转正评估主表状态为已转正 所以后续操作根据转正评估表状态判断加上probation_channel_type一起判断才行）
     * @param $staff_info_id
     * @param bool $check_entry
     * @return bool
     */
    public function is_non_front_line_probation($staff_info_id, bool $check_entry = true): bool
    {
        if (empty($staff_info_id)) {
            return false;
        }
        $log = $this->getDI()->get('logger');
        if ($check_entry) {
            $probationData = HrProbationModel::findFirst([
                'columns'    => ['staff_info_id,probation_channel_type'],
                'conditions' => "staff_info_id = :staff_info_id:",
                'bind'       => [
                    'staff_info_id' => $staff_info_id,
                ],
            ]);
            $probationData = empty($probationData) ? [] : $probationData->toArray();
            if (empty($probationData['probation_channel_type']) || $probationData['probation_channel_type'] != HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE) {
                $log->write_log("is_non_front_line_probation ,因hr_probation表中字段已标明不属于转正评估非一线员工了 probationData: " . json_encode($probationData),'info');
                return false;
            }
        } else {
            $staffInfo = HrStaffInfoModel::findFirst([
                'columns'    => [
                    'staff_info_id',
                    'state',
                    'node_department_id',
                    'sys_store_id',
                    'job_title',
                    'hire_type',
                ],
                'conditions' => "staff_info_id = :staff_info_id:",
                'bind'       => [
                    'staff_info_id' => $staff_info_id,
                ],
            ]);
            if (empty($staffInfo)) {
                $log->write_log("is_non_front_line_probation ,因缺少员工信息,不属于转正评估非一线员工 staffInfo: null",'info');
                return false;
            }
            $staffInfo = $staffInfo->toArray();
            if (empty($staffInfo['node_department_id']) ||
                empty($staffInfo['sys_store_id']) ||
                empty($staffInfo['job_title']) ||
                $staffInfo['state'] == HrStaffInfoModel::STATE_RESIGN ||
                empty($staffInfo['hire_type'])
            ) {
                $log->write_log("is_non_front_line_probation ,因缺少员工信息,不属于转正评估非一线员工 staffInfo:" . json_encode($staffInfo),'info');
                return false;
            }
            if ($staffInfo['hire_type'] != HrStaffInfoModel::HIRE_TYPE_1) {
                $log->write_log("is_non_front_line_probation ,因雇佣类型非正式员工,不属于转正评估非一线员工 staffInfo:" . json_encode($staffInfo),'info');
                return false;
            }
            $job_title_r = HrJobDepartmentRelationModel::findFirst([
                'conditions' => 'department_id = :department_id: and job_id = :job_id: and position_type in ({position_type:array})',
                'bind'       => [
                    'department_id' => $staffInfo['node_department_id'],
                    'job_id'        => $staffInfo['job_title'],
                    'position_type' => [
                        HrJobDepartmentRelationModel::POSITION_TYPE_2,
                        HrJobDepartmentRelationModel::POSITION_TYPE_3,
                    ],
                ],
            ]);
            if (empty($job_title_r)) {
                $log->write_log("is_non_front_line_probation ,因部门职位不属于总部职能或者一线职能，不属于转正评估非一线员工 staffInfo:" . json_encode($staffInfo),'info');
                return false;
            }
        }
        $probation_staff = (new SettingEnvServer())->getSetVal('probation_staff', ',');
        if (in_array($staff_info_id, $probation_staff)) {
            $log->write_log("is_non_front_line_probation ,因在转正白名单里,不属于转正评估非一线员工 staff_info_id:" . $staff_info_id . " ,probation_staff:" . json_encode($probation_staff),'info');
            return false;
        }

        $log->write_log("is_non_front_line_probation ,属于转正评估非一线员工 staff_info_id:" . $staff_info_id,'info');
        return true;
    }

    /**
     * @param $staffInfoIds
     * @return array
     */
    public function getNonFrontLineProbationStaff($staffInfoIds, $stage = 1): array
    {
        if (!empty($staffInfoIds)) {
            $probationData        = HrProbationModel::find([
                'columns'    => ['staff_info_id,probation_channel_type'],
                'conditions' => "staff_info_id in ({staff_ids:array})",
                'bind'       => [
                    'staff_ids' => $staffInfoIds,
                ],
            ])->toArray();
            $probationData        = array_column($probationData, 'probation_channel_type', 'staff_info_id');
            $targetDetail     = HrProbationTargetDetailModel::find([
                'conditions' => "setting_state = :setting_state: and send_state = :send_state: and sign_state = :sign_state: and is_deleted = :is_deleted: and staff_info_id in ({staff_ids:array})",
                'bind'       => [
                    'setting_state' => HrProbationTargetDetailModel::SETTING_STATE_FINISH,
                    'send_state'    => HrProbationTargetDetailModel::SEND_STATE_FINISH,
                    'sign_state'    => HrProbationTargetDetailModel::SIGN_STATE_FINISH,
                    'is_deleted'    => 0,
                    'staff_ids' => $staffInfoIds,
                ],
                'order'      => 'stage asc',
            ])->toArray();
            $targetDetail     = $targetDetail ? array_column($targetDetail, null, 'staff_info_id') : [];
            $staffInfoIdsStr  = implode($staffInfoIds, ',');
            $sql              = "SELECT * from hr_probation_audit where staff_info_id in ({$staffInfoIdsStr}) order by audit_level asc";
            $probation_audit  = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $_probation_audit = [];
            foreach ($probation_audit as $v) {
                $_probation_audit[$v['staff_info_id']][$v['cur_level']] = $v['score'] ?? '';
            }
            $_targetDetail = [];
            foreach ($staffInfoIds as $staff_id) {
                if ($stage == 2) {
                    if (!empty($_probation_audit[$staff_id][2])) {
                        $_targetDetail[$staff_id]['target_info'] = $_probation_audit[$staff_id][2];
                    } else {
                        $_targetDetail[$staff_id]['target_info'] = !empty($targetDetail[$staff_id]['target_info']) ? $this->formatTargetInfoJson($targetDetail[$staff_id]['target_info'],$targetDetail[$staff_id]['duty_info'] ?? '',$_probation_audit[$staff_id][1] ?? '') : ($_probation_audit[$staff_id][1] ?? '');
                    }
                } else {
                    if (!empty($_probation_audit[$staff_id][1])) {
                        $_targetDetail[$staff_id]['target_info'] = $_probation_audit[$staff_id][1];
                    } else {
                        $_targetDetail[$staff_id]['target_info'] = $this->formatTargetInfoJson($targetDetail[$staff_id]['target_info'] ?? '',$targetDetail[$staff_id]['duty_info'] ?? '');
                    }
                }
            }
        } else {
            $probationData     = [];
            $_targetDetail = [];
        }
        $probation_staff = (new SettingEnvServer())->getSetVal('probation_staff', ',');
        return ['probation_data' => $probationData, 'probation_staff' => $probation_staff, 'target' => $_targetDetail];
    }

    public function formatTargetInfoJson($data,$duty_info,$score = '')
    {
        if (empty($data)) {
            return '';
        }
        $data = json_decode($data, true);
        foreach ($data as &$v) {
            $v['first_score']       = '';
            $v['first_score_text']  = '';
            $v['second_score']      = '';
            $v['second_score_text'] = '';
            $v['third_score']       = '';
            $v['third_score_text']  = '';
        }
        if (!empty($score)){
            $score_1 = json_decode($score, true);
            $return_data['1'] = $score_1[1] ?? '';
        }else{
            $return_data['1']['target_score']     = $data;
            $return_data['1']['count_score']      = '';
            $return_data['1']['count_score_text'] = '';
            $return_data['1']['good_job']         = '';
            $return_data['1']['no_good_job']      = '';
            $return_data['1']['action_plan']      = '';
        }
        $return_data['2']['target_score']     = $data;
        $return_data['2']['count_score']      = '';
        $return_data['2']['count_score_text'] = '';
        $return_data['2']['good_job']         = '';
        $return_data['2']['no_good_job']      = '';
        $return_data['2']['action_plan']      = '';
        $return_data['duty_info']             = $duty_info ?? '';
        return json_encode($return_data, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 详情 - 非一线
     * @param $staffInfoId
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function detailNonFrontLine($param): array
    {
        if (empty($param['staff_info_id'])) {
            throw new ValidationException($this->t->_('param_error'));
        }
        if (empty($this->is_non_front_line_probation($param['staff_info_id']))) {
            throw new ValidationException($this->t->_('probation_status_err_1'));
        }
        $probation = HrProbationModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $param['staff_info_id'],
            ],
        ]);
        if (empty($probation)) {
            throw new BusinessException($this->t->_('data_error'));
        }
        if (!empty($param['id'])){
            $probation_audit = HrProbationAuditModel::findFirst([
                'conditions' => "id = :id:",
                'bind'       => [
                    'id' => $param['id'],
                ],
            ]);
            $probation_audit                 = $probation_audit->toArray();
        }else{
            $probation_audit['score'] = $this->formatScoreInfo($probation->id,$param['staff_info_id']);
        }
        $return_data['stage_score_data'] = !empty($probation_audit['score']) ? json_decode($probation_audit['score'],
            true) : [];
        $return_data['audit_logs']       = $this->getNonFrontLineProbationLogs($probation->id);
        $return_data['staff_data']       = (new StaffRepository())->getStaffInfoAllOne($param['staff_info_id']);
        // 是否显示第三次评分
        $return_data['staff_data']['is_show_third'] = false;
        if (
            isset($return_data['staff_data']['job_title_grade_v2']) && 
            $return_data['staff_data']['job_title_grade_v2'] > $this->job_grade_exceed
        ){
            $return_data['staff_data']['is_show_third'] = true;
        }
        $return_data['staff_data']['probation_channel_type'] = $probation->probation_channel_type ?? 1;
        $return_data['staff_data']['first_evaluate_start'] = $probation->first_evaluate_start ?? '';
        $return_data['staff_data']['first_evaluate_end'] = $probation->first_evaluate_end ?? '';
        $return_data['staff_data']['second_evaluate_start'] = $probation->second_evaluate_start ?? '';
        $return_data['staff_data']['second_evaluate_end'] = $probation->second_evaluate_end ?? '';
        $return_data['staff_data']['cur_level'] = $probation->cur_level ?? 1;
        $return_data['staff_data']['probation_status'] = $probation->status ?? 1;
        $return_data = $this->by_show_deadline($probation->id,$param['operator_id'] ?? '',$return_data);
        return $return_data;
    }

    /**
     * @param $probation_id
     * @param $staff_info_id
     * @return false|string
     * @throws BusinessException
     */
    public function formatScoreInfo($probation_id,$staff_info_id)
    {
        $probation_audit = HrProbationAuditModel::find([
            'conditions' => "probation_id = :probation_id:",
            'bind'       => [
                'probation_id' => $probation_id,
            ],
            'order'      => 'cur_level desc,audit_level desc,id desc',
        ])->toArray();
        if (empty($probation_audit)){
            $targetDetail     = HrProbationTargetDetailModel::findFirst([
                'conditions' => "setting_state = :setting_state: and send_state = :send_state: and sign_state = :sign_state: and is_deleted = :is_deleted: and staff_info_id = :staff_info_id:",
                'bind'       => [
                    'setting_state' => HrProbationTargetDetailModel::SETTING_STATE_FINISH,
                    'send_state'    => HrProbationTargetDetailModel::SEND_STATE_FINISH,
                    'sign_state'    => HrProbationTargetDetailModel::SIGN_STATE_FINISH,
                    'is_deleted'    => 0,
                    'staff_info_id'    => $staff_info_id,
                ],
                'order'      => 'stage asc',
            ]);
            if (empty($targetDetail)){
                throw new BusinessException($this->t->_('data_error'));
            }
            $targetDetail = $targetDetail->toArray();
            return $this->formatTargetInfoJson($targetDetail['target_info'] ?? '',$targetDetail['duty_info'] ?? '');
        }
        $_score_2 = [];
        $_score_1 = [];
        $_score_tmp = [];
        foreach ($probation_audit as $value){
            $score = json_decode($value['score'], true);
            $duty_info = $score['duty_info'] ?? '';
            if ($value['cur_level'] == HrProbationModel::CUR_LEVEL_SECOND && empty($_score_2[$value['audit_status']])){
                $_score_2[$value['audit_status']] = $score[HrProbationModel::CUR_LEVEL_SECOND];
            }
            if ($value['cur_level'] == HrProbationModel::CUR_LEVEL_FIRST && empty($_score_1[$value['audit_status']])){
                $_score_1[$value['audit_status']] = $score[HrProbationModel::CUR_LEVEL_FIRST];
                $_score_tmp = $score[HrProbationModel::CUR_LEVEL_SECOND];
            }
        }
        $return_data['1'] = !empty($_score_1[2]) ? $_score_1[2] : (!empty($_score_1[1]) ? $_score_1[1] : (!empty($_score_1[3]) ? $_score_1[3] : []));
        $return_data['2'] = !empty($_score_2[2]) ? $_score_2[2] : (!empty($_score_2[1]) ? $_score_2[1] : (!empty($_score_2[3]) ? $_score_2[3] : $_score_tmp));
        $return_data['duty_info'] = $duty_info ?? '';
        return json_encode($return_data, JSON_UNESCAPED_UNICODE);
    }

    /**
     * @param $probation_id
     * @param $operator_id
     * @param $return_data
     * @return array
     */
    public function by_show_deadline($probation_id,$operator_id,$return_data): array
    {
        $return_data['staff_data']['first_deadline'] = '';
        $return_data['staff_data']['second_deadline'] = '';
        $return_data['staff_data']['first_probation_audit_status'] = '';
        $return_data['staff_data']['second_probation_audit_status'] = '';
        if (empty($operator_id) || empty($probation_id)){
            return $return_data;
        }
        $probation_audit = HrProbationAuditModel::find([
            'conditions' => "probation_id = :probation_id: and audit_id = :audit_id:",
            'bind'       => [
                'probation_id' => $probation_id,
                'audit_id' => $operator_id,
            ],
        ])->toArray();
        if (empty($probation_audit)){
            return $return_data;
        }
        $probation_audit = array_column($probation_audit,null,'cur_level');
        $return_data['staff_data']['first_deadline'] = !empty($probation_audit[1]['deadline_at']) ? $probation_audit[1]['deadline_at'] : '';
        $return_data['staff_data']['first_probation_audit_status'] = !empty($probation_audit[1]['audit_status']) ? $probation_audit[1]['audit_status'] : '';
        $return_data['staff_data']['second_deadline'] = !empty($probation_audit[2]['deadline_at']) ? $probation_audit[2]['deadline_at'] : '';
        $return_data['staff_data']['second_probation_audit_status'] = !empty($probation_audit[2]['audit_status']) ? $probation_audit[2]['audit_status'] : '';
        return $return_data;
    }

    /**
     * 日志
     * @param $probation_id
     * @return array
     */
    public function getNonFrontLineProbationLogs($probation_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('
        audit.staff_info_id,
        audit.audit_id,
        audit.audit_level,
        audit.cur_level,
        audit.audit_status,
        audit.status,
        audit.created_at,
        audit.updated_at,
        audit.is_active,
        hi.name as audit_staff_name,
        n.name as audit_node_department_name,
        job.job_name as audit_job_name
        ');
        $builder->orderBy('audit.updated_at asc');
        $builder->from(['audit' => HrProbationAuditModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, "audit.audit_id = hi.staff_info_id", 'hi');
        $builder->leftJoin(HrJobTitleModel::class, 'job.id = hi.job_title', 'job');
        $builder->leftJoin(SysDepartmentModel::class, 'n.id = hi.node_department_id', 'n');
        $builder->where('audit.probation_id = ' . $probation_id);
        $probation_audit = $builder->getQuery()->execute()->toArray();

        foreach ($probation_audit as $k => $v) {
            $probation_audit[$k]['updated_at'] = $v['updated_at'];
            $probation_audit[$k]['created_at'] = $v['created_at'];
            if ($v['cur_level'] == self::CUR_LEVEL_SECOND) {
                $probation_audit[$k]['audit_status_text'] = $this->getSecondStatusText($v['status'],
                    $v['audit_status']);
            } else {
                $probation_audit[$k]['audit_status_text'] = $this->t->_("probation_audit_status_" . $v['audit_status']);
            }
            //如果最后一个且是待评估 这里从老详情迁移的逻辑
            if ($k == count($probation_audit) - 1 && $v['audit_status'] == 1) {
                $probation_audit[$k]['is_now'] = '1';
            } else {
                $probation_audit[$k]['is_now'] = '0';
            }
        }
        return $probation_audit ?? [];
    }
    
    /**
     * hcm的编辑-分数提交
     * @param $params
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function scoreSubmitNonFrontLineHcm($params): array
    {
        if (empty($params['staff_info_id']) || empty($params['stage_score_data']) || empty($params['operator_id'])) {
            throw new ValidationException($this->t->_('param_error'));
        }
        if (empty($this->is_non_front_line_probation($params['staff_info_id']))) {
            throw new ValidationException($this->t->_('probation_status_err_1'));
        }
        $probation = HrProbationModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $params['staff_info_id'],
            ],
        ]);
        if (empty($probation) || $probation->probation_channel_type != HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE) {
            throw new BusinessException($this->t->_('data_error'));
        }
        if (!empty($probation->status) && $probation->status == HrProbationModel::STATUS_FORMAL){
            throw new BusinessException($this->t->_('probation_status_err_1'));
        }
        $probation        = $probation->toArray();
        $stage_score_data = $params['stage_score_data'];
        
        $staffInfo = HrStaffInfoModel::findFirst([
            'columns'    => [
                'job_title_grade_v2',
            ],
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $params['staff_info_id'],
            ],
        ]);
        $scoreData = $this->checkScoreData($stage_score_data,$staffInfo->job_title_grade_v2 ?? 0);
        if (!isset($scoreData[2]['second_score'])) {
            throw new ValidationException($this->t->_('check_score_data_err', ['tip' => 'second_score']));
        }
        if ($scoreData['job_title_grade_v2'] > $this->job_grade_exceed){
            $count_score = !empty($scoreData[2]['third_score']) ? $scoreData[2]['third_score'] : 0;
        }else{
            $count_score = !empty($scoreData[2]['second_score']) ? $scoreData[2]['second_score'] : 0;
        }
        $count_score = !empty($count_score) ? intval(bcsub($count_score, "0", 0)) : 0;
        // 从这里开始逻辑部分是从 方法：auditBi迁移过来的，需求文档并没有说明
        $item                 = []; //hr_probation表
        $item['updated_at']   = gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600);
        $item['second_score'] = $count_score;
        $item['is_system']    = 1;
        $item['cur_level']    = 2;
        if ($count_score >= 3) {
            $item['status']        = HrProbationModel::STATUS_PASS;    //已通过
            $item['second_status'] = HrProbationModel::SECOND_STATUS_PASS;
            //如果当前时间，大于转正时间，且分数大于等于3分，直接已转正
            if (strtotime($item['updated_at']) >= strtotime($probation['formal_at'])) {
                $item['status']          = HrProbationModel::STATUS_FORMAL;
                $item['formal_staff_id'] = $params['operator_id'];
            }
        } else {
            $item['status']        = HrProbationModel::STATUS_NOT_PASS;    //未通过
            $item['second_status'] = HrProbationModel::SECOND_STATUS_NOT_PASS;
        }
        // 如果状态为已经通过，但是还未到转正日期 则后续转正脚本需要跑，插入转正评估数据等不许在在执行
        if ($item['status'] == HrProbationModel::STATUS_PASS) {
            $item['is_system'] = 2;
        }
        //下一级评审，内容大部分相同
        $tmp                  = [];
        $tmp['probation_id']  = $probation['id'];
        $tmp['staff_info_id'] = $probation['staff_info_id'];
        $tmp['tpl_id']        = $probation['tpl_id'];
        $tmp['audit_id']      = $params['operator_id'];
        $tmp['audit_level']   = 2;
        $tmp['cur_level']     = self::CUR_LEVEL_SECOND;
        $tmp['audit_status']  = self::AUDIT_STATUS_DEAL;
        $tmp['status']        = $item['status'];
        $tmp['score']         = json_encode($stage_score_data, JSON_UNESCAPED_UNICODE);;
        $tmp['created_at']  = $item['updated_at'];
        $tmp['updated_at']  = $item['updated_at'];
        $tmp['deadline_at'] = gmdate("Y-m-d", time() + ($this->add_hour) * 3600);

        $db = $this->getDI()->get("db");
        $db->begin();
        try {
            $db->updateAsDict("hr_probation", $item, ["conditions" => 'id=' . intval($probation['id'])]);
            $db->insertAsDict("hr_probation_audit", $tmp);
            if ($item['status'] == HrProbationModel::STATUS_FORMAL) {
                $this->putFormalLog($params['operator_id'], $probation['staff_info_id'], $probation['status'],
                    HrProbationModel::STATUS_FORMAL);
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            throw new BusinessException('Please try again later');
        }
        return $this->checkReturn(['data' => true]);
    }

    /**
     * 转正评估非一线分数枚举
     * @return array[]
     */
    public function probation_score_enumerate(): array
    {
        return [
            ['value' => 1, 'label' => 'C'],
            ['value' => 2, 'label' => 'B-'],
            ['value' => 3, 'label' => 'B'],
            ['value' => 4, 'label' => 'B+'],
            ['value' => 5, 'label' => 'A'],
        ];
    }

    /**
     * 检测分数结构
     * @throws ValidationException
     */
    public function checkScoreData($data,$job_title_grade_v2 = 0): array
    {
        if (
            empty($data) ||
            !isset($data['duty_info']) ||
            !isset($data['1']) || 
            !isset($data['2']) ||
            !isset($data['1']['target_score']) ||
            !isset($data['1']['count_score']) || 
            !isset($data['1']['count_score_text']) ||
            !isset($data['2']['target_score']) ||
            !isset($data['2']['count_score']) ||
            !isset($data['2']['count_score_text'])) {
            throw new ValidationException($this->t->_('check_score_data_err', ['tip' => 'base data']));
        }
        $return_data = [
            1 => [
                'third_score'  => 0,
                'second_score' => 0,
                'first_score'  => 0,
            ],
            2 => [
                'third_score'  => 0,
                'second_score' => 0,
                'first_score'  => 0,
            ],
        ];
        $score_text               = $this->probation_score_enumerate();
        $score_text               = array_column($score_text, 'label', 'value');
        $score_text[0]            = '';
        foreach ($data as $k => $v) {
            if (in_array($k, [1, 2])) {
                $stage_weight = 0;
                foreach ($v['target_score'] as $vv) {
                    if (empty($vv['weight']) ||
                        !isset($vv['first_score']) ||
                        !isset($vv['first_score_text']) ||
                        !isset($vv['second_score']) ||
                        !isset($vv['second_score_text']) ||
                        !isset($vv['third_score']) ||
                        !isset($vv['third_score_text'])
                    ) {
                        throw new ValidationException($this->t->_('check_score_data_err', ['tip' => 'base data']));
                    }
                    $stage_weight += $vv['weight'];
                    if (!empty($vv['first_score']) && (!isset($score_text[$vv['first_score']]) || $score_text[$vv['first_score']] != $vv['first_score_text'])) {
                        throw new ValidationException($this->t->_('check_score_data_err', ['tip' => 'first_score']));
                    }
                    if (!empty($vv['second_score']) && (!isset($score_text[$vv['second_score']]) || $score_text[$vv['second_score']] != $vv['second_score_text'])) {
                        throw new ValidationException($this->t->_('check_score_data_err', ['tip' => 'second_score']));
                    }
                    if (!empty($vv['third_score']) && (!isset($score_text[$vv['third_score']]) || $score_text[$vv['third_score']] != $vv['third_score_text'])) {
                        throw new ValidationException($this->t->_('check_score_data_err', ['tip' => 'third_score']));
                    }
                    if ($k == 1) {
                        if (!empty($vv['third_score'])) {
                            $return_data[1]['third_score'] += $vv['third_score'] * $vv['weight'] / 100;
                        } elseif (!empty($vv['second_score'])) {
                            $return_data[1]['second_score'] += $vv['second_score'] * $vv['weight'] / 100;
                        } elseif (!empty($vv['first_score'])) {
                            $return_data[1]['first_score'] += $vv['first_score'] * $vv['weight'] / 100;
                        }
                    }
                    if ($k == 2) {
                        if (!empty($vv['third_score'])) {
                            $return_data[2]['third_score'] += $vv['third_score'] * $vv['weight'] / 100;
                        } elseif (!empty($vv['second_score'])) {
                            $return_data[2]['second_score'] += $vv['second_score'] * $vv['weight'] / 100;
                        } elseif (!empty($vv['first_score'])) {
                            $return_data[2]['first_score'] += $vv['first_score'] * $vv['weight'] / 100;
                        }
                    }
                }
                if ($stage_weight != 100) {
                    throw new ValidationException($this->t->_('check_score_data_err', ['tip' => 'weight']));
                }
                if ($job_title_grade_v2 > $this->job_grade_exceed){
                    $count_score[$k] = !empty($return_data[$k]['third_score']) ? intval(bcsub($return_data[$k]['third_score'], "0", 0)) :(!empty($return_data[$k]['second_score']) ? intval(bcsub($return_data[$k]['second_score'], "0", 0)) : (!empty($return_data[$k]['first_score']) ? intval(bcsub($return_data[$k]['first_score'], "0", 0)) : 0));
                }else{
                    $count_score[$k] = !empty($return_data[$k]['second_score']) ? intval(bcsub($return_data[$k]['second_score'], "0", 0)) : (!empty($return_data[$k]['first_score']) ? intval(bcsub($return_data[$k]['first_score'], "0", 0)) : 0);
                }
                if (!isset($score_text[$count_score[$k]]) || $score_text[$count_score[$k]] != $v['count_score_text']) {
                    throw new ValidationException($this->t->_('check_score_data_err', ['tip' => 'count_score']));
                }
            }
        }
        $return_data['job_title_grade_v2'] = $job_title_grade_v2;
        return $return_data;
    }
    
    /**
     * by的分数提交
     * @param $params
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function scoreSubmitNonFrontLine($params): array
    {
        if (empty($params['id']) || empty($params['stage_score_data']) || empty($params['operator_id']) || empty($params['staff_info_id'])) {
            throw new ValidationException($this->t->_('param_error'));
        }
        if (empty($this->is_non_front_line_probation($params['staff_info_id']))) {
            throw new ValidationException($this->t->_('probation_status_err_1'));
        }
        $probation_audit = HrProbationAuditModel::findFirst([
            'conditions' => "id = :id:",
            'bind'       => [
                'id' => $params['id'],
            ],
        ]);
        if (empty($probation_audit) || empty($probation_audit->audit_id) || $probation_audit->audit_id != $params['operator_id']) {
            throw new BusinessException($this->t->_('data_error'));
        }
        $probation_audit = $probation_audit->toArray();
        if ($probation_audit['audit_status'] != HrProbationAuditModel::AUDIT_STATUS_PENDING) {
            throw new ValidationException($this->t->_('status_changed'));
        }
        $probation = HrProbationModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $probation_audit['staff_info_id'],
            ],
        ]);
        if (empty($probation)) {
            throw new BusinessException($this->t->_('data_error'));
        }
        $probation = $probation->toArray();
        if (!empty($probation['status']) && $probation['status'] == HrProbationModel::STATUS_FORMAL){
            throw new BusinessException($this->t->_('probation_status_err_1'));
        }
        if ($probation['is_system'] == 1) {
            throw new ValidationException('system auto');
        }
        $manager_id      = $this->getManagerId($probation_audit['audit_id']);
        $staffInfo       = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $probation_audit['staff_info_id'] ?? '',
            ],
        ]);
        if (empty($staffInfo)){
            throw new ValidationException('system auto');
        }
        $staffInfo       = $staffInfo->toArray();
        $deadline_at_one = $params['deadline_at_one'] ?? 3;//第一阶段上上级过期时间
        $is_fail_msg     = $params['is_fail_msg'] ?? 1;    //未通过是否发送消息给被评估员工上级，以及被评估员工所属HRBP 目前只有 Id 发送  2 是发送
        $evaluate_time   = $this->duration_day;            //递增天数
        $is_send         = $probation_audit['audit_level'] == self::AUDIT_LEVEL_1 && !empty($manager_id);
        if (isset($staffInfo['job_title_grade_v2']) && $staffInfo['job_title_grade_v2'] > $this->job_grade_exceed &&
            $probation_audit['version'] == $this->version) {
            $cpo_staff_info_id = $this->cpo_staff_id;
            $is_send           = !($probation_audit['audit_id'] == $cpo_staff_info_id || $probation_audit['audit_level'] >= self::AUDIT_LEVEL_3);
            $manager_id        = $probation_audit['audit_level'] >= self::AUDIT_LEVEL_2 ? $cpo_staff_info_id : $manager_id;
            $evaluate_time     = $this->duration_day_exceed;//递增的截止天数
        }
        $scoreData            = $this->checkScoreData($params['stage_score_data'],$staffInfo['job_title_grade_v2'] ?? 0);
        // 二阶段上级也会变更试用期状态 上上级会覆盖上级 cpo会覆盖上上级
        $count_score = !empty($scoreData[$probation_audit['cur_level']]['third_score']) ? $scoreData[$probation_audit['cur_level']]['third_score'] :(!empty($scoreData[$probation_audit['cur_level']]['second_score']) ? $scoreData[$probation_audit['cur_level']]['second_score'] : (!empty($scoreData[$probation_audit['cur_level']]['first_score']) ? $scoreData[$probation_audit['cur_level']]['first_score'] : 0));
        $count_score = !empty($count_score) ? intval(bcsub($count_score, "0", 0)) : 0;
        $item                 = [];                                                        //hr_probation表
        $data                 = [];                                                        //hr_probation_audit表
        $data['score']        = json_encode($params['stage_score_data'], JSON_UNESCAPED_UNICODE);//score存json
        $data['audit_status'] = 2;
        $item['updated_at']   = gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600);
        $data['updated_at']   = $item['updated_at'];
        // 产品说:这里和之前老逻辑保持一致
        if ($probation_audit['cur_level'] == self::CUR_LEVEL_FIRST) {
            $item['first_score']        = $count_score;
            $item['first_audit_status'] = HrProbationModel::FIRST_AUDIT_STATUS_RUN;
            if (!($is_send && !empty($manager_id))) {
                $item['first_audit_status'] = HrProbationModel::FIRST_AUDIT_STATUS_DONE;
                if ($count_score >= 3) {
                    $item['first_status'] = HrProbationModel::FIRST_STATUS_PASS;
                }
            }
        } else {
            $item['second_score'] = $count_score;
            if ($count_score >= 3) {
                $item['status'] = self::STATUS_PASS;    //已通过
                if ($item['updated_at'] > $probation['formal_at']) {
                    $is_send = false;
                    $item['status']          = self::STATUS_FORMAL;
                    $item['formal_staff_id'] = $probation_audit['audit_id'];
                    $item['formal_at']       = gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600);
                }
            } else {
                $item['status'] = self::STATUS_NOT_PASS;    //未通过
            }
            $data['status'] = $item['status'];
            //不发了 赋值终态
            if (!($is_send && !empty($manager_id))) {
                $item['second_audit_status'] = HrProbationModel::SECOND_AUDIT_STATUS_DONE;
                if (in_array($item['status'],[self::STATUS_PASS,self::STATUS_FORMAL])) {
                    $item['second_status'] = HrProbationModel::SECOND_STATUS_PASS;
                }
            }
        }
        
        $db = $this->getDI()->get("db");
        try {
            $db->begin();
            $db->updateAsDict("hr_probation_audit", $data, ["conditions" => "id=" . intval($probation_audit['id'])]);
            $db->updateAsDict("hr_probation", $item,
                ["conditions" => 'id=' . intval($probation_audit['probation_id'])]);
            //如果可以评估 并且有下一级评估人 并且没有转正
            if ($is_send && !empty($manager_id)) {
                //下一级评审，内容大部分相同
                $tmp                  = [];
                $tmp['probation_id']  = $probation_audit['probation_id'];
                $tmp['staff_info_id'] = $probation_audit['staff_info_id'];
                $tmp['audit_id']      = $manager_id;
                $tmp['audit_level']   = ((int)$probation_audit['audit_level']) + 1;
                $tmp['cur_level']     = $probation_audit['cur_level'];
                $tmp['audit_status']  = 1;
                $tmp['status']        = 0;
                $tmp['score']         = $data['score'];
                $tmp['created_at']    = $item['updated_at'];
                $tmp['updated_at']    = $item['updated_at'];
                $tmp['version']       = $probation_audit['version'];

                if ($probation['is_active'] == 1){
                    $now_date = gmdate("Y-m-d 00:00:00", time() + ( $this->add_hour)*3600);
                    $tmp['deadline_at']        = $this->getDateByDays($now_date, 3, 1);
                }else{
                    $day                = $evaluate_time[$probation_audit['cur_level']][$tmp['audit_level']] ?? $deadline_at_one;
                    $tmp['deadline_at'] = $this->getDateByDays($probation_audit['deadline_at'], $day, 1);
                }
                $db->insertAsDict("hr_probation_audit", $tmp);
            }

            //如果是上上级评估需要发送消息
            if ($probation_audit['audit_level'] == self::AUDIT_LEVEL_2) {
                //插入消息表
                $msg_data = [
                    'probation_audit_id' => $probation_audit['id'],
                    'created_at'         => gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600),
                    'updated_at'         => gmdate("Y-m-d H:i:s", time() + ($this->add_hour) * 3600),
                ];
                $res      = $this->getDI()->get('db')->insertAsDict("hr_probation_message", $msg_data);
                if (!$res) {
                    throw new \Exception($this->getTranslation()->_('no_server'));
                }
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->getDI()->get('logger')->write_log('scoreSubmitNonFrontLineBy message:' . $e->getMessage() . '-----' . $e->getLine());
            throw new BusinessException($this->t->_('db_update_fail'));
        }
        if (!empty($tmp)) {
            $this->push_notice_higher($tmp['audit_id'], $tmp['staff_info_id']);
        }
        if ($probation_audit['cur_level'] == self::CUR_LEVEL_SECOND && $probation_audit['audit_level'] == 2 && $item['status'] == self::STATUS_NOT_PASS) {
            $this->sendMsgToManagerByStaffId($probation_audit['staff_info_id'], $this->getManagerId($probation_audit['staff_info_id']));
        }
        if (isset($item['status']) && $item['status'] == self::STATUS_FORMAL) {
            $this->putFormalLog($probation_audit['audit_id'], $probation_audit['staff_info_id'], $probation['status'],
                self::STATUS_FORMAL);
        }
        if ($item['first_audit_status'] == HrProbationModel::FIRST_AUDIT_STATUS_DONE || $item['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE){
            // 发送阶段完成发送签字消息 被评估人和被评估人的上级
            $_cur_level = 1;
            if ($item['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE){
                $_cur_level = 2;
            }
            $rpc_params = [
                'staff_info_id' => $probation_audit['staff_info_id'],
                'customize_cur_level' => $_cur_level,
            ];
            $hcmCli      = new ApiClient('hcm_rpc', '', 'probation_stage_done_message',$this->lang);
            $hcmCli->setParams($rpc_params);
            $returnData = $hcmCli->execute();
            if($returnData["result"]['code'] != 1) {
                throw new BusinessException($this->t->_('probation_err_1'));
            }
        }
        return $this->checkReturn(['data' => true]);
    }


    public function scoreEnumerateNonFrontLine()
    {
        return [
            ['value' => 5, 'label' => $this->t->_('scoreenumeratenonfrontline_5')],
            ['value' => 4, 'label' => $this->t->_('scoreenumeratenonfrontline_4')],
            ['value' => 3, 'label' => $this->t->_('scoreenumeratenonfrontline_3')],
            ['value' => 2, 'label' => $this->t->_('scoreenumeratenonfrontline_2')],
            ['value' => 1, 'label' => $this->t->_('scoreenumeratenonfrontline_1')],
        ];
    }

    /**
     * @return array[]
     * @throws BusinessException
     * @throws ValidationException
     */
    public function oneImportNonFrontLine()
    {
        if (!isCountry('TH')){
            throw new ValidationException('目前仅支持TH,其他国家需要改逻辑涉及到算转正日期和一二阶段开始和结束时间');
        }
        $logger                         = $this->getDI()->get('logger');
        $one_import_non_front_line_file = (new SettingEnvServer())->getSetVal('one_import_non_front_line');
        $path                           = $this->downloadFile($one_import_non_front_line_file,
            'one_import_non_front_line', 'one_import_non_front_line_file.xlsx');
        $config                         = ['path' => dirname($path)];
        $fileRealName                   = basename($path);
        $excel                          = new \Vtiful\Kernel\Excel($config);
        $excel->openFile($fileRealName)->openSheet();
        $excel->setSkipRows(0);
        $excel_data       = $excel->getSheetData();
        $staff_excel_data = [];
        $staff_info_ids   = [];
        foreach ($excel_data as $key => $value) {
            $_staff_info_id = $value[0] ?? '';
            if (empty($_staff_info_id)) {
                continue;
            }
            $staff_excel_data[$_staff_info_id]['duty_info']              = !empty($value[1]) ? $value[1] : ($staff_excel_data[$_staff_info_id]['duty_info'] ?? '');
            $staff_excel_data[$_staff_info_id]['target'][$key]['name']   = $value[2] ?? '';
            $staff_excel_data[$_staff_info_id]['target'][$key]['info']   = $value[3] ?? '';
            $staff_excel_data[$_staff_info_id]['target'][$key]['weight'] = $value[4] ?? '';
            $staff_excel_data[$_staff_info_id]['target'][$key]['score']  = $value[5] ?? '';
            $staff_excel_data[$_staff_info_id]['good_job']               = !empty($value[6]) ? $value[6] : ($staff_excel_data[$_staff_info_id]['good_job'] ?? '');
            $staff_excel_data[$_staff_info_id]['no_good_job']            = !empty($value[7]) ? $value[7] : ($staff_excel_data[$_staff_info_id]['no_good_job'] ?? '');
            $staff_excel_data[$_staff_info_id]['action_plan']            = !empty($value[8]) ? $value[8] : ($staff_excel_data[$_staff_info_id]['action_plan'] ?? '');
            $staff_info_ids[$_staff_info_id]                             = $_staff_info_id;
        }
        if (!empty($staff_info_ids)) {
            $staff_data = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in ({staffs:array})',
                'bind'       => [
                    'staffs' => array_values($staff_info_ids),
                ],
            ])->toArray();
            $staff_data = array_column($staff_data, null, 'staff_info_id');

            $target_data = HrProbationTargetModel::find([
                'conditions' => 'staff_info_id in ({staff_info_id:array}) and is_deleted = :is_deleted:',
                'bind'       => [
                    'staff_info_id' => array_values($staff_info_ids),
                    'is_deleted'    => 0,
                ],
            ])->toArray();
            $target_data = array_column($target_data, null, 'staff_info_id');
        } else {
            throw new BusinessException('oneImportNonFrontLine staff_info_id is empty');
        }
        $error_staff_data   = [];
        $success_staff_data = [];
        
        $probation_score_enumerate_label = array_column($this->probation_score_enumerate(), 'label');
        $probation_score_enums_v_l = array_column($this->probation_score_enumerate(), 'value', 'label');
        $probation_score_enums_l_v = array_column($this->probation_score_enumerate(), 'label', 'value');
        $db = $this->getDI()->get("db");
        foreach ($staff_excel_data as $staff_info_id => $item) {
            try {
                $db->begin();
                if (
                    in_array($staff_info_id,$error_staff_data) ||
                    empty($item['duty_info']) ||
                    mb_strlen($item['duty_info']) > 10000 ||
                    empty($staff_data[$staff_info_id]) ||
                    empty($staff_data[$staff_info_id]['hire_date']) ||
                    strtotime($staff_data[$staff_info_id]['hire_date']) < strtotime("-5 months", time()) ||
                    count($item['target']) > 50 ||
                    empty($staff_data[$staff_info_id]['manger']) ||
                    empty($item['target']) ||
                    !$this->is_non_front_line_probation($staff_info_id, false)
                ) {
                    throw new \Exception("该员工duty_info或hire_date或manger或是否非一线,不正确");
                }
                $_staff_score  = [];
                $_staff_weight = [];
                $_staff_target = [];
                $probation_score = [];
                $count_score = 0;
                $_staff_target_score_1 = [];
                $_staff_target_score_2 = [];
                foreach ($item['target'] as $v) {
                    if (
                        empty($v['name']) ||
                        mb_strlen($v['name']) > 10000 ||
                        empty($v['info']) ||
                        mb_strlen($v['info']) > 10000 ||
                        empty($v['weight']) ||
                        !is_numeric($v['weight']) ||
                        (!empty($v['score']) && !in_array($v['score'], $probation_score_enumerate_label))
                    ) {
                        throw new \Exception("该员工name或info或weight或score不正确");
                    }
                    $_staff_weight[] = $v['weight'];
                    $_staff_score[]  = !empty($v['score']) ? $v['score'] : 'DDD';
                    $_staff_target[] = [
                        'name'   => $v['name'],
                        'info'   => $v['info'],
                        'weight' => $v['weight'],
                    ];
                    $_first_score_text = !empty($v['score']) ? $v['score'] : '';
                    $_first_score = $probation_score_enums_v_l[$_first_score_text] ?? '';
                    $_staff_target_score_1[] = [
                        'name'   => $v['name'],
                        'info'   => $v['info'],
                        'weight' => $v['weight'],
                        'first_score' => $_first_score,
                        'first_score_text' => $_first_score_text,
                        'second_score' => '',
                        'second_score_text' => '',
                        'third_score' => '',
                        'third_score_text' => '',
                    ];
                    $_staff_target_score_2[] = [
                        'name'   => $v['name'],
                        'info'   => $v['info'],
                        'weight' => $v['weight'],
                        'first_score' => '',
                        'first_score_text' => '',
                        'second_score' => '',
                        'second_score_text' => '',
                        'third_score' => '',
                        'third_score_text' => '',
                    ];
                    if ($_first_score){
                        $count_score += $_first_score * $v['weight'] / 100;
                    }
                }
                if (
                    empty($_staff_weight) ||
                    array_sum($_staff_weight) != 100 ||
                    (array_intersect($_staff_score, $probation_score_enumerate_label) && in_array('DDD', $_staff_score)) ||
                    empty($_staff_target_score_1) ||
                    (
                        array_intersect($_staff_score, $probation_score_enumerate_label) && 
                        (
                            empty($item['good_job']) ||
                            mb_strlen($item['good_job']) > 10000 || 
                            empty($item['no_good_job']) ||
                            mb_strlen($item['no_good_job']) > 10000 ||
                            empty($item['action_plan']) ||
                            mb_strlen($item['action_plan']) > 10000
                        )
                    )
                ) {
                    throw new \Exception("该员工权重活或分数或做的好与不好不正确");
                }
                if (!empty($target_data[$staff_info_id])) {
                    throw new \Exception("该员工目标已存在,跳过");
                }
                
                $count_score = !empty($count_score) ? intval(bcsub($count_score, "0", 0)) : '';
                $count_score_text = $probation_score_enums_l_v[$count_score] ?? '';
                $probation_score[1]['target_score'] = $_staff_target_score_1;
                $probation_score[1]['count_score'] = $count_score;
                $probation_score[1]['count_score_text'] = $count_score_text;
                $probation_score[1]['good_job'] = $item['good_job'] ?? '';
                $probation_score[1]['no_good_job'] = $item['no_good_job'] ?? '';
                $probation_score[1]['action_plan'] = $item['action_plan'] ?? '';
                $probation_score[2]['target_score'] = $_staff_target_score_2;
                $probation_score[2]['count_score'] = '';
                $probation_score[2]['count_score_text'] = '';
                $probation_score[2]['good_job'] = '';
                $probation_score[2]['no_good_job'] = '';
                $probation_score[2]['action_plan'] = '';
                $probation_score['duty_info'] = $item['duty_info'];
                $this->insertTargetTask($staff_info_id,$_staff_target,$item['duty_info'],$db);
                $this->deleteProbationScoreTask($staff_info_id,$db);
                $this->insertProbationScoreTask($staff_info_id,$probation_score,$staff_data[$staff_info_id],$db);
                $db->commit();
                $success_staff_data[] = $staff_info_id;
            } catch (\Exception $e) {
                $db->rollback();
                $logger->write_log('oneImportNonFrontLine staff_info_id:' . $staff_info_id . ' 错误原因:'.$e->getMessage());
                $error_staff_data[] = $staff_info_id;
                continue;
            }
        }
        $logger->write_log([
            'function' => 'oneImportNonFrontLine',
            'success_staff_data' => $success_staff_data,
            'error_staff_data' => $error_staff_data,
        ], 'info');
        return [
            'error_staff_data'   => $error_staff_data,
            'success_staff_data' => $success_staff_data,
        ];
    }

    /**
     * @throws \Exception
     */
    protected function insertProbationScoreTask($staff_info_id,$probation_score,$staff_data,$db)
    {
        $job_title_grade_v2 = empty($staff_data['job_title_grade_v2']) ? 0 : $staff_data['job_title_grade_v2'];
        $hire_date = $staff_data['hire_date'];
        $hr_probation_target_data = [
            'staff_info_id' => $staff_info_id,
            'hire_type' => $staff_data['hire_type'],
            'created_at'    => '2025-03-25 00:00:00',
            'updated_at'    => '2025-03-25 00:00:00',
            'probation_channel_type'=>HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE,
            'formal_at'=>$this->getFormalDate($hire_date,$staff_data['job_title_grade_v2']),
        ];
        if ($job_title_grade_v2 <= $this->job_grade_exceed){
            $hr_probation_target_data['first_evaluate_start'] = $this->getDateByDays($hire_date,$this->evaluate_day[1],1);
            $hr_probation_target_data['first_evaluate_end'] = $this->getDateByDays($hire_date,$this->first_check_days,1);
            $hr_probation_target_data['second_evaluate_start'] = $this->getDateByDays($hire_date,$this->evaluate_day[2],1);
            $hr_probation_target_data['second_evaluate_end'] = $this->getDateByDays($hire_date,$this->second_check_days,1);
        }else{
            $hr_probation_target_data['first_evaluate_start'] = $this->getDateByDays($hire_date,$this->evaluate_day_exceed[1],1);
            $hr_probation_target_data['first_evaluate_end'] = $this->getDateByDays($hire_date,$this->first_check_days_exceed,1);
            $hr_probation_target_data['second_evaluate_start'] = $this->getDateByDays($hire_date,$this->evaluate_day_exceed[2],1);
            $hr_probation_target_data['second_evaluate_end'] = $this->getDateByDays($hire_date,$this->second_check_days_exceed,1);
        }
        $db->insertAsDict("hr_probation", $hr_probation_target_data);
        $probation_id = $db->lastInsertId();
        if (empty($probation_id)){
            throw new \Exception("插入hr_probation失败");
        }
        if (!empty($probation_score[1]['count_score'])){
            $evaluate_day = $this->evaluate_day;
            $evaluate_time =(int)$staff_data['job_title_grade_v2'] <= $this->job_grade_exceed ? $this->duration_day : $this->duration_day_exceed;
            $hire_date_begin = $this->getDateByDays($staff_data['hire_date'], $evaluate_day[1]);
            $status = 3;
            $first_status = 0;
            if ($probation_score[1]['count_score'] >= 3){
                $status = 2;
                $first_status = 1;
            }
            $hr_probation_target_data = [
                "probation_id" => $probation_id,
                "staff_info_id" => $staff_info_id,
                'audit_id' => $staff_data['manger'],
                'tpl_id' => null,
                'score' => json_encode($probation_score, JSON_UNESCAPED_UNICODE),
                'created_at' => '2025-03-25 00:00:00',
                'updated_at' => '2025-03-25 00:00:00',
                'deadline_at' => $this->getDateByDays($hire_date_begin, $evaluate_time['1']['1'] ?? 3, 1),
                'version'=>$this->version,
                'audit_status'=>HrProbationAuditModel::AUDIT_STATUS_PROCESSED,
                'status'=>$status,
            ];
            $db->insertAsDict("hr_probation_audit", $hr_probation_target_data);
            $db->updateAsDict("hr_probation", [
                "first_score" => $probation_score[1]['count_score'],
                "first_status" => $first_status,
                "first_audit_status" => HrProbationModel::FIRST_AUDIT_STATUS_DONE,
                'updated_at'    => '2025-03-25 00:00:00',
            ], ["conditions" => 'id='.$probation_id]);
        }
    }
    
    protected function getFormalDate($hireDate, $job_title_grade_v2)
    {
        if (empty($job_title_grade_v2)){
            $job_title_grade_v2 = 0;
        }
        $formal_days = $job_title_grade_v2 <= 17 ?  $this->formal_days : $this->formal_days_exceed;
        return $this->getDateByDays($hireDate,$formal_days - 1, 1);
    }

    /**
     * @param $staff_info_id
     * @param $db
     * @return void
     */
    protected function deleteProbationScoreTask($staff_info_id,$db)
    {
        $probation_sql = "delete from hr_probation where staff_info_id = {$staff_info_id}";
        $db->execute($probation_sql);
        $probation_audit_sql = "delete from hr_probation_audit where staff_info_id = {$staff_info_id}";
        $db->execute($probation_audit_sql);
    }
    
    /**
     * @param $staff_info_id
     * @param $_staff_target
     * @param $duty_info
     * @param $db
     * @return void
     * @throws \Exception
     */
    protected function insertTargetTask($staff_info_id,$_staff_target,$duty_info,$db)
    {
        $hr_probation_target_data = [
            'staff_info_id' => $staff_info_id,
            'setting_state' => HrProbationTargetModel::SETTING_STATE_FINISH,
            'send_state'    => HrProbationTargetModel::SEND_STATE_FINISH,
            'sign_state'    => HrProbationTargetModel::SIGN_STATE_FINISH,
            'created_at'    => '2025-03-25 00:00:00',
            'updated_at'    => '2025-03-25 00:00:00',
        ];
        $db->insertAsDict("hr_probation_target", $hr_probation_target_data);
        $target_id = $db->lastInsertId();
        if (!$target_id) {
            throw new \Exception("插入hr_probation_target失败" . json_encode($hr_probation_target_data,JSON_UNESCAPED_UNICODE));
        }
        $target_info_json                = json_encode($_staff_target,JSON_UNESCAPED_UNICODE);
        $hr_probation_target_detail_data = [
            'target_id'     => $target_id,
            'staff_info_id' => $staff_info_id,
            'target_info'   => $target_info_json,
            'duty_info'     => $duty_info,
            'setting_state' => HrProbationTargetModel::SETTING_STATE_FINISH,
            'setting_time'  => '2025-03-25 00:00:00',
            'send_state'    => HrProbationTargetModel::SEND_STATE_FINISH,
            'sign_state'    => HrProbationTargetModel::SIGN_STATE_FINISH,
            'created_at'    => '2025-03-25 00:00:00',
            'updated_at'    => '2025-03-25 00:00:00',
        ];
        $db->insertAsDict("hr_probation_target_detail", $hr_probation_target_detail_data);
    }

    /**
     * @param $param
     * @return array
     * @throws ValidationException
     */
    public function getProbationStageDoneMsg($param): array
    {
        $userId     = $param['user_id'] ?? 0;
        $msgId      = $param['msg_id'] ?? '';
        if (empty($msgId)){
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        $server = new BackyardServer($this->lang, $this->timezone);
        // 验证该员工是否收到签字消息
        $sign_msg = $server->get_staff_sign_msg([
            'msg_id'=>$msgId,
            'staff_info_id'=>$userId,
        ]);
        if (empty($sign_msg) || $sign_msg['staff_info_id'] != $userId) {
            throw new ValidationException('员工没有收到该签字消息');
        }
        $messagePdf = MessagePdfModel::findFirst([
            'conditions' => "msg_id = :msg_id:",
            'bind'       => ['msg_id' => $msgId],
        ]);
        if (empty($messagePdf)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }
        $messagePdf = $messagePdf->toArray();
        $return_data['msg_id'] = $msgId;
        $return_data['state'] = $messagePdf['state'] == 1 ? 1 : 0;
        $return_data['pdf_url'] = $messagePdf['pdf_url'] ?? '';
        if (!empty($messagePdf['sign_url'])){
            $return_data['pdf_url'] = $messagePdf['sign_url'];
        }
        return $return_data;
    }
    
    /**
     * @param $param
     * @return bool
     * @throws ValidationException
     */
    public function submitProbationStageDoneMsg($param): bool
    {
        $userId     = $param['user_id'] ?? 0;
        $msgId      = $param['msg_id'] ?? '';
        $signImgUrl      = $param['sign_img_url'] ?? '';
        if (empty($msgId)){
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        $server = new BackyardServer($this->lang, $this->timezone);
        // 验证该员工是否收到签字消息
        $sign_msg = $server->get_staff_sign_msg([
            'msg_id'=>$msgId,
            'staff_info_id'=>$userId,
        ]);
        if (empty($sign_msg) || $sign_msg['staff_info_id'] != $userId) {
            throw new ValidationException('员工没有收到该签字消息');
        }
        $messagePdfObj = MessagePdfModel::findFirst([
            'conditions' => "msg_id = :msg_id:",
            'bind'       => ['msg_id' => $msgId],
        ]);
        if (empty($messagePdfObj) || empty($messagePdfObj->form_data_json) || empty($messagePdfObj->business_id)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }
        if (!empty($messagePdfObj->sign_img_url)){
            throw new ValidationException($this->getTranslation()->_('message_sign_err'));
        }
        $messagePdf = $messagePdfObj->toArray();
        $form_data = json_decode($messagePdf['form_data_json'], true);
        $probation = HrProbationModel::findFirst([
            'columns'    => 'first_stage_done_msg_id,second_stage_done_msg_id,first_stage_done_manager_msg_id,second_stage_done_manager_msg_id',
            'conditions' => "id = :id:",
            'bind'       => ['id' => $messagePdf['business_id']],
        ]);
        if (empty($probation)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }
        $probation = $probation->toArray();
        $current_key = '';
        foreach ($probation as $key => $value){
            if ($value == $msgId){
                $current_key = $key;
                break;
            }
        }
        if (empty($current_key)){
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }
        $current_key = substr($current_key,0,-7);
        $_pdfImgData = [
            ['name'=>'staff_sign_url','url'=>$form_data['staff_sign_url']],
            ['name'=>'manager_sign_url','url'=>$form_data['manager_sign_url']],
            ['name'=>'first_done_staff_sign_url','url'=>$form_data['first_done_staff_sign_url']],
            ['name'=>'first_done_manager_sign_url','url'=>$form_data['first_done_manager_sign_url']],
            ['name'=>'second_done_staff_sign_url','url'=>$form_data['second_done_staff_sign_url']],
            ['name'=>'second_done_manager_sign_url','url'=>$form_data['second_done_manager_sign_url']],
        ];
        $new_time = time();
        $_sign_time = gmdate('Y-m-d H:i:s', $new_time + ($this->add_hour) * 3600);
        switch ($current_key) {
            case 'first_stage_done':
                $_pdfImgData[array_search('first_done_staff_sign_url', array_column($_pdfImgData, 'name'))]['url'] = $signImgUrl;
                $form_data['first_done_staff_sign_time'] = $_sign_time;
                $form_data['first_done_staff_sign_url'] = $signImgUrl;
                break;
            case 'second_stage_done':
                $_pdfImgData[array_search('second_done_staff_sign_url', array_column($_pdfImgData, 'name'))]['url'] = $signImgUrl;
                $form_data['second_done_staff_sign_time'] = $_sign_time;
                $form_data['second_done_staff_sign_url'] = $signImgUrl;
                break;
            case 'first_stage_done_manager':
                $_pdfImgData[array_search('first_done_manager_sign_url', array_column($_pdfImgData, 'name'))]['url'] = $signImgUrl;
                $form_data['first_done_manager_sign_time'] = $_sign_time;
                $form_data['first_done_manager_sign_url'] = $signImgUrl;
                break;
            case 'second_stage_done_manager':
                $_pdfImgData[array_search('second_done_manager_sign_url', array_column($_pdfImgData, 'name'))]['url'] = $signImgUrl;
                $form_data['second_done_manager_sign_time'] = $_sign_time;
                $form_data['second_done_manager_sign_url'] = $signImgUrl;
                break;
        }
        $db = $this->getDI()->get("db");
        $db->begin();
        try {
            $pdf_header_footer_setting = $form_data['pdf_options'];
            $pdf_template_url = $form_data['pdf_temp_url'];
            $file_name = $userId.time();
            $pdf_file_data             = (new formPdfServer())->generatePdf($pdf_template_url, $form_data, $_pdfImgData,
                $file_name, $pdf_header_footer_setting,'attchment');
            if (empty($pdf_file_data['object_url'])) {
                throw new \Exception('pdf error ');
            }
            $messagePdfObj->sign_url = $pdf_file_data['object_url'];
            $messagePdfObj->sign_time = gmdate('Y-m-d H:i:s', $new_time);
            $messagePdfObj->sign_img_url = $signImgUrl;
            $messagePdfObj->state = MessagePdfModel::STATE_SUCCESS;
            $messagePdfObj->save();
            // 将消息 已读
            $backyardServer = new BackyardServer($this->lang, $this->timeZone);
            $backyardServer->has_read_operation($msgId,true);
            $db->commit();
            return true;
        } catch (\Exception $e) {
            $db->rollback();
            $this->getDI()->get("logger")->write_log([
                'function'  => __FUNCTION__,
                'error' => $e->getMessage(),
            ]);
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }
    }
    
    
}