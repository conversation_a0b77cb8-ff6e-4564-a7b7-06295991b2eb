<?php

namespace FlashExpress\bi\App\Server;


use App\Country\Tools;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RestClient;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffNotRefundedSuspensionTaskModel;
use FlashExpress\bi\App\Models\backyard\ReinstatementRequestModel;
use FlashExpress\bi\App\Models\backyard\SuspensionManageLogModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use Phalcon\Mvc\Model\ResultsetInterface;

class ReinstatementServer extends AuditBaseServer
{

    public $staffInfo;

    /**
     * @param $params
     * @return bool
     */
    public function addRequest($params)
    {
        [$checkResult,$log] = $this->checkCanApply($params['staff_info_id']);

        //菲律宾期望日期 今天（含） - +7（含）之外不能申请
        if(isCountry('PH')
            && ($params['expected_date'] < date('Y-m-d') || $params['expected_date'] > date('Y-m-d',strtotime('+7 day')))
        ){
            throw new ValidationException($this->getTranslation()->_('reinstatement_date_notice_ph'));
        }

        if ($checkResult){
            try {
                $this->getDI()->get('db')->begin();
                $staff_info_id = $params['staff_info_id'];
                $log_id = $log['id'];
                $this->updateReinstatementRequestOld($staff_info_id, $log_id);
                //保存申请数据
                $model = new ReinstatementRequestModel();
                $model->serial_no = 'RN' . $this->getRandomId();
                $model->staff_info_id = $staff_info_id;
                $model->origin_state = HrStaffInfoModel::STATE_3;//暂时
                $model->reason = $params['reason'];
                $model->reason_explanation = $params['reason_explanation'];
                $model->stop_duties_date = date('Y-m-d', strtotime($log['stop_duties_date']));
                $model->expected_date = $params['expected_date'];
                $model->attach = json_encode($params['attach']);
                $model->ref_id = $log_id;
                $model->state = enums::APPROVAL_STATUS_PENDING;
                $model->handled = ReinstatementRequestModel::NOT_HANDLED;
                $extend['time_out'] = date('Y-m-d 00:00:00',strtotime('+3 day'));
                $model->time_out = $extend['time_out'];
                $model->submitter_id = $params['staff_info_id'];
                if (!$model->save()){
                    throw new InnerException("数据保存错误: 复职申请 ". json_encode($params));
                }
                //审批流
                $appServer = new ApprovalServer($this->lang, $this->timeZone);
                $app = $appServer->create($model->id, AuditListEnums::APPROVAL_TYPE_REINSTATEMENT, $params['staff_info_id'], null,$extend);
                if (!$app){
                    throw new InnerException("审批流创建错误：复职申请 ".json_encode($params));
                }
                $this->getDI()->get('db')->commit();
                return true;
            } catch (\Exception $e) {
                $this->getDI()->get('db')->rollback();
                $this->logger->write_log("复职申请提交失败：{$e->getMessage()}");
                return false;
            }
        }
        return false;
    }



    /**
     * @param $staffId
     * @param $checkDuplicate
     * @return array|void
     * @throws BusinessException
     */
    public function checkCanApply($staffId, $checkDuplicate= true)
    {
        $staffInfo = (new StaffRepository($this->lang))->getStaffInfoByStaffId($staffId,'hsi.staff_info_id,hsi.state,hsi.hire_type');
        if (empty($staffInfo)){
            //return [false, $this->getTranslation()->t('员工信息不存在')];
            throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg1'));//员工信息不存在
        }
        if ($staffInfo['state'] == HrStaffInfoModel::STATE_2){
            //return [false, $this->getTranslation()->t('您已离职，如需申请请线下联系主管')];
            throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg2'));//您已离职，如需申请请线下联系主管
        }
        if ($staffInfo['state'] == HrStaffInfoModel::STATE_1){
            //return [false, $this->getTranslation()->t('您已在职，请返回登录页面登录')];
            throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg3'));//您已在职，请返回登录页面登录
        }
        if ($staffInfo['state'] == HrStaffInfoModel::STATE_3){
            $log = $this->getLatestSuspensionLog($staffId);
            if (!$log){
                //return [false, $this->getTranslation()->t('没有停职记录')];
                throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg4'));//没有停职记录
            }
            $approveStatus = [
                enums::APPROVAL_STATUS_PENDING,
                enums::APPROVAL_STATUS_APPROVAL,
                enums::APPROVAL_STATUS_REJECTED,
                enums::APPROVAL_STATUS_CANCEL,
                enums::APPROVAL_STATUS_TIMEOUT,
            ];
            $count = $this->getRequests($approveStatus,['ref_id'=>$log['id']])->count();
            if ($checkDuplicate  && $count > 0){
                //return [false, $this->getTranslation()->t('一次停职只能申请一次恢复在职申请')];
                throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg5'));//一次停职只能申请一次恢复在职申请
            }
            if (!in_array($log['stop_duty_reason'],['6','7'])){
                $msg_key = 'reinstatement_check_msg6';
                if ($staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
                    $msg_key = 'reinstatement_check_msg6_individual_contractor';
                }
                //return [false, $this->getTranslation()->t('当前申请恢复在职功能只支持由于连续旷工和未回公款导致的停职，您的停职原因不符，请线下联系主管')];
                throw new BusinessException($this->getTranslation()->t($msg_key));//当前申请恢复在职功能只支持由于连续旷工和未回公款导致的停职，您的停职原因不符，请线下联系主管
            }
            //验证ms是否允许
            $this->onJobCheekMs($staffId);

            return [true,$log];
        }
    }

    /**
     * 恢复在职检查ms
     * @throws BusinessException
     */
    public function onJobCheekMs($staff_id): bool
    {
        $staffs = [$staff_id];
        //查询子账号数据
        $sql = "
        select s.sub_staff_info_id 
        from hr_staff_apply_support_store as  s
        inner join hr_staff_not_refunded_suspension_task as t on s.`sub_staff_info_id` = t.`staff_info_id`
        where s.staff_info_id = :staff_info_id and s.sub_staff_info_id > 0 ";

        $subStaffs = $this->getDI()->get('db_rby')->query($sql,
            ['staff_info_id' => $staff_id])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($subStaffs)) {
            $staffs = array_merge($staffs, array_column($subStaffs, 'sub_staff_info_id'));
        }
        //查询是否有未回款金额
        $fau                = new RestClient('fau');
        $params['staffIds'] = array_values(array_unique($staffs));
        $result             = $fau->execute(RestClient::METHOD_POST, '/svc/staff/receivable_and_remittance/check',
            $params, ['Accept-Language' => $this->lang]);

        if (!isset($result['code'])) {
            throw new BusinessException($this->getTranslation()->t('server_error'));
        }

        if ($result['code'] != 1) {
            throw new BusinessException($result['message']);
        }
        return true;
    }



    /**
     * @inheritDoc
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $request = $this->getRequestById($auditId);
        $head = [
            'title'       => (new AuditlistRepository($this->lang, $this->timeZone))->getAudityType(AuditListEnums::APPROVAL_TYPE_REINSTATEMENT),
            'id'          => $request->id,
            'staff_id'    => $request->staff_info_id,
            'type'        => AuditListEnums::APPROVAL_TYPE_REINSTATEMENT,
            'created_at'  => DateHelper::utcToLocal($request->created_at),
            'updated_at'  => DateHelper::utcToLocal($request->updated_at),
            'status'      => $request->state,
            'serial_no'   => $request->serial_no ?? '',
        ];
        $staff_info = (new StaffServer())->getStaffInfoById($request->staff_info_id);
        $ss = new SysStoreServer();
        $storeRegionPieceId = $ss->getStoreRegionPiece($staff_info['sys_store_id']);
        if (empty($storeRegionPieceId['manage_region'])) {
            $storeRegionPiece['manage_region'] = '';
        } else {
            $storeRegionName = $ss->getStoreRegionName($storeRegionPieceId['manage_region']);
            $storeRegionPiece['manage_region'] = $storeRegionName['manage_region'];
        }
        if (empty($storeRegionPieceId['manage_piece'])) {
            $storeRegionPiece['manage_piece'] = '';
        } else {
            $storePieceName = $ss->getStorePieceName($storeRegionPieceId['manage_piece']);
            $storeRegionPiece['manage_piece'] = $storePieceName['manage_piece'];
        }
        $key = 'stop_duty_reason'.'_'.$request->reason;
        $detail = [
            'apply_parson'         => sprintf('%s ( %s )', $staff_info['staff_name'] ?? '',
                $staff_info['staff_info_id'] ?? ''),
            'apply_department'     => sprintf('%s - %s', $staff_info['department_name'] ?? '',
                $staff_info['job_name'] ?? ''),
            'staff_store'          => $staff_info['store_name'] ?? '',
            'region_piece_name'    => $storeRegionPiece['manage_region'] . '-' . $storeRegionPiece['manage_piece'],
            'staff_job_title'      => $staff_info['job_name'] ?? '',
            'hire_type'            => $this->getTranslation()->_('hire_type_' . $staff_info['hire_type']), // 雇佣类型,
            'stop_duties_date'     => $request->stop_duties_date,
            'stop_duty_reason'     => $this->getSuspensionReason()[$request->reason] ?? '',
            $key                   => $request->reason_explanation,
            'expected_return_date' => $request->expected_date ?? '',
            'attach'               => json_decode($request->attach, true),
        ];

        $returnData['data']['suspension_log_list'] = $this->getSuspensionLogList($request->staff_info_id); //停职记录
        $returnData['data']['head'] = $head;
        $returnData['data']['detail'] = $this->format($detail);

        return $returnData;
    }

    /**
     * @inheritDoc
     */
    public function genSummary(int $auditId, $user)
    {
        $request = $this->getRequestById($auditId);
        $staff = (new StaffServer())->getStaffInfoById($request->staff_info_id);
        $data = [
            [
                'key' => 'staff_store',
                'value' => $staff->store_name ?? '',
            ],
            [
                'key' => 'staff_job_title',
                'value' => $staff->job_name ?? '',
            ],
            [
                'key' => 'stop_duty_reason',
                'value' => $request->reason,
            ],
            [
                'key' => 'created_at',
                'value' => $request->created_at,
            ],
        ];
        return $data;
    }

    /**
     * @inheritDoc
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal){
            $request = $this->getRequestById($auditId);
            if ($request){
                //更新审批状态
                $request->state = $state;
                $res = true;
                $staffServer = new StaffServer();
                $this->staffInfo =  $staffServer->getStaffInfoById($request->staff_info_id);
                //如果 非停职状态 不发消息 下面用
                if($this->staffInfo['state'] != enums::$service_status['suspension']){
                    $res = false;
                }
                if ($state == enums::APPROVAL_STATUS_APPROVAL){
                    $today = date('Y-m-d');
                    $request->effective_date = $request->expected_date;
                    //更新生效时间 https://flashexpress.feishu.cn/wiki/EwM4w5O1CiavztkQK0McVbKsnzh 审批通过立即生效 期望日期之前审批通过
                    if (strtotime($request->expected_date) <= strtotime($today)) {
                        $request->handled = ReinstatementRequestModel::HANDLED;
                        if ($res) {
                            $res = $this->reinstatement($request, $today);
                            if ($res !== false) {//停职状态 记录 实际恢复时间
                                //修改实际恢复时间
                                $request->effective_date = date('Y-m-d H:i:s');
                                [$result1, $result2] = $res;
                                $this->logger->write_log("恢复在职审批通过 立即生效 {$request->staff_info_id} {$result1},{$result2}",
                                    'info');
                            }
                        }
                    }
                }
                //驳回原因回写
                if($state != enums::APPROVAL_STATUS_APPROVAL){
                    $request->reject_reason = $extend['remark'] ?? '';
                }
                $request->save();
                //如果 已经是在职了 不发消息
                if($res === false){
                    return true;
                }
                //发送短信提醒
                $this->composeAndSendMsg($state,$request,$extend);
            }
        }
    }

    /**
     * @inheritDoc
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $request = $this->getRequestById($auditId);

        return [
            'reason' => $request ? $request->reason:0,
        ];
    }

    /**
     * @param $requestId
     * @return mixed
     */
    public function getRequestById($requestId)
    {
        $request = ReinstatementRequestModel::findFirst($requestId);

        return $request;
    }

    /**
     * @param $staffId
     * @param $suspensionId
     * @return mixed
     */
    public function getRequestByStaffId($staffId,$suspensionId)
    {
        $request = ReinstatementRequestModel::findFirst(
            [
                'conditions' => "staff_info_id=:staff_id: and ref_id=:sus_id: and handled=0",
                'bind'=>['staff_id'=> $staffId,'sus_id'=> $suspensionId],
                'order' => "id desc",
            ]
        );
        return $request;
    }

    /**
     * 根据审批和处理状态获取申请
     * @param $auditStatus
     * @param array $params
     * @return ResultsetInterface
     */
    public function getRequests($auditStatus,$params=[])
    {
        $conditions = "state in ({status:array})";
        $bind = ['status'=> $auditStatus];
        if (isset($params['handled'])){
            $conditions .= " and handled=:handled:";
            $bind['handled'] = $params['handled'];
        }
        if (!empty($params['ref_id'])){
            $conditions .= " and ref_id=:ref_id:";
            $bind['ref_id'] = $params['ref_id'];
        }
        if (!empty($params['expected_date'])){
            $conditions .= " and expected_date=:expected_date:";
            $bind['expected_date'] = $params['expected_date'];
        }
        if (!empty($params['effective_date'])){
            $conditions .= " and effective_date=:effective_date:";
            $bind['effective_date'] = $params['effective_date'];
        }
        //超时任务调用
        if (!empty($params['time_out'])){
            $conditions .= " and time_out <= :time_out: ";
            $bind['time_out'] = $params['time_out'];
        }

        //查询指定id
        if (!empty($params['ids'])){
            $conditions .= " and id in ({ids:array})";
            $bind['ids'] = $params['ids'];
        }

        $list = ReinstatementRequestModel::find(
            [
                'conditions'=> $conditions,
                'bind' => $bind,
                'order' => 'id desc',
            ]
        );

        return $list;
    }

    /**
     * @param $id
     * @return \Phalcon\Mvc\Model
     */
    public function getSuspensionLog($id)
    {
        $log = SuspensionManageLogModel::findFirst($id);
        return $log;
    }
    /**
     * 获取停职记录
     * @param $staffId
     * @return array|false
     */
    public function getLatestSuspensionLog($staffId)
    {
        $log = SuspensionManageLogModel::findFirst(
            [
                'conditions'=> "staff_info_id=:staff_id: and is_new=1",
                'bind' => ['staff_id'=> $staffId,],
                'order' => 'id desc',
            ]
        );
        if ($log){
            return $log->toArray();
        }
        return false;
    }

    /**
     * @param $params
     * @param $staffId
     * @return array
     * @throws \Exception
     */
    public function audit($params,$staffId)
    {
        $auditType = AuditListEnums::APPROVAL_TYPE_REINSTATEMENT;
        $approval_id = $staffId;//操作人工号
        $audit_id = $params['audit_id'];
        $status = intval($params['status']);

        $app_server = new ApprovalServer($this->lang,$this->timeZone);

        if ($status == enums::$audit_status['approved']) {//审核通过
            $res = $app_server->approval($audit_id, $auditType, $approval_id);
        } elseif ($status == enums::$audit_status['dismissed']) {//驳回
            $res = $app_server->reject($audit_id, $auditType, $params['reject_reason'], $approval_id);
        } elseif ($status == enums::$audit_status['revoked']) {//撤销
            $res = $app_server->cancel($audit_id, $auditType, $params['reason'], $approval_id);
        }else{
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }
        if (empty($res)) {
            $this->logger->write_log('audit_update_status error ' . json_encode($params).json_encode($res),'info');
            return $this->checkReturn(-3, 'server error');
        }else{
            return $this->checkReturn(1);
        }
    }

    /**
     * @param $status
     * @param $request
     * @param $extend
     * @return void
     */
    private function composeAndSendMsg($status,$request, $extend)
    {
        $staffServer = new StaffServer();
        if(empty($this->staffInfo)){
            $this->staffInfo =  $staffServer->getStaffInfoById($request->staff_info_id);
        }
        if(empty($this->staffInfo)){
            return true;
        }
        switch ($status){
            case enums::APPROVAL_STATUS_APPROVAL:
                //审批通过处理；
                $effectiveDate = date('Y-m-d', strtotime($request->effective_date));
                //给员工发短信
                $this->sendSms([$this->staffInfo], 'reinstatement_approve_sms', ['date' => $effectiveDate]);
                //给上级发消息
                $manager = $staffServer->getStaffMangerId($request->staff_info_id);
                if (!empty($manager)){
                    $manager = array_column($manager,'value');

                    $this->sendMessage($manager, 'reinstatement_approve_title', 'reinstatement_approve_manager_msg', [
                        'staff_name'    => $this->staffInfo['staff_name'],
                        'staff_info_id' => $this->staffInfo['staff_info_id'],
                    ], [
                        'staff_name'    => $this->staffInfo['staff_name'],
                        'staff_info_id' => $this->staffInfo['staff_info_id'],
                        'dep_name'      => $this->staffInfo['department_name'],
                        'job_name'      => $this->staffInfo['job_name'],
                        'store_name'    => $this->staffInfo['store_name'],
                        'date'          => $effectiveDate,
                    ]);
                }

                //给HRBP发消息
                $hrbps = (new WorkflowServer($this->lang,$this->timeZone))->findHRBP($this->staffInfo['node_department_id'],['store_id'=>$this->staffInfo['sys_store_id']]);
                if ($hrbps){

                    $this->sendMessage(explode(',', $hrbps), 'reinstatement_approve_title', 'reinstatement_approve_hrbp_msg', [
                        'staff_name'    => $this->staffInfo['staff_name'],
                        'staff_info_id' => $this->staffInfo['staff_info_id'],
                    ], [
                        'staff_name'    => $this->staffInfo['staff_name'],
                        'staff_info_id' => $this->staffInfo['staff_info_id'],
                        'dep_name'      => $this->staffInfo['department_name'],
                        'job_name'      => $this->staffInfo['job_name'],
                        'store_name'    => $this->staffInfo['store_name'],
                        'date'          => $effectiveDate,
                    ]);
                }

                break;
            case enums::APPROVAL_STATUS_REJECTED:
                //审批驳回处理
                //给员工发短信
                $this->sendSms([$this->staffInfo],'reinstatement_reject_sms',['reason'=> $extend['remark'] ?? '']);
                break;
            case enums::APPROVAL_STATUS_TIMEOUT:
                //审批超时处理
                //给员工发短信
                $this->sendSms([$this->staffInfo],'reinstatement_timeout_sms',[]);
                break;
            default:
                break;
        }
        if (!empty($staff)){

        }
    }

    /**
     * @return array|mixed
     */
    public function getSuspensionReason()
    {
        $client = new ApiClient('hcm_rpc','','getSuspensionReason',$this->lang);
        $client->setParams([]);
        $result = $client->execute();
        if ($result['result']['code'] == 1){
            $reasons =  $result['result']['data'];
            $reasons = array_column($reasons,'label','value');
            return $reasons;
        }
        return [];
    }

    protected function getSmsKey($smsKey, $hire_type)
    {
        if (!isCountry('PH')) {
            return $smsKey;
        }

        if ($hire_type != HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            return $smsKey;
        }
        switch ($smsKey) {
            case 'reinstatement_notify_sms':
            case 'reinstatement_notify_viber':
                $result =  isCountry('PH') ? 'reinstatement_notify_viber_individual_contractor': 'reinstatement_notify_sms_individual_contractor';
                break;
            case 'reinstatement_timeout_sms':
                $result = 'reinstatement_timeout_sms_individual_contractor';
                break;
            case 'reinstatement_reject_sms':
                $result = 'reinstatement_reject_sms_individual_contractor';
                break;
            case 'reinstatement_approve_sms':
                $result = 'reinstatement_approve_sms_individual_contractor';
                break;
            default:
                $result = $smsKey;
        }
        return $result;
    }


    /**
     * @param $staffs
     * @param $smsKey
     * @param $smsPlaceholder
     * @return void
     */
    public function sendSms($staffs,$smsKey,$smsPlaceholder, $lang = 'en')
    {
        //$staffServer = new StaffServer();
        $t = $this->getTranslation($lang);
        foreach ($staffs as $v){
            //$t = $this->getTranslation($staffServer->getLanguage($v['staff_info_id']));
            $is_lnt = (new StaffServer())->isLntStaff($v['staff_info_id']);
            $sms_src = $is_lnt ? 'reinstatement_job_lnt' : 'reinstatement_job';
            $data=[
                'mobile'=> $v['mobile'],
                'msg' => $t->t($this->getSmsKey($smsKey,$v['hire_type']), $smsPlaceholder),
                'nation' => strtoupper(env('country_code')),
            ];
            //viber
            if (isCountry('PH')) {
                $data['type'] =  0;// 文本消息， 固定类型
                $data['service_provider'] =  9;// 服务商 Viber 固定值
            }
            $sms_rpc = (new ApiClient('sms_rpc', '', 'send', $this->lang,$sms_src));
            $sms_rpc->setParams($data);
            $return = $sms_rpc->execute();
            if (isset($return['result'])) {
                $this->logger->write_log("短信发送成功：". json_encode(['data'=>$data,'result'=>$return]),'info');
            } else {
                $this->logger->write_log("短信发送失败：". json_encode(['data'=>$data,'result'=>$return]),'notice');
            }
        }

    }

    /**
     * 发送消息
     * @param $staffs
     * @param $titleKey
     * @param $contentKey
     * @param $titlePlaceholder
     * @param $contentPlaceholder
     * @return bool
     */
    public function sendMessage($staffs,$titleKey,$contentKey,$titlePlaceholder,$contentPlaceholder)
    {
        $staffServer = new StaffServer();
        foreach ($staffs as $v){
            $t = $this->getTranslation($staffServer->getLanguage($v));
            $staffInfo = $staffServer->getStaffInfoById($v);
            if ($staffInfo['state'] != enums::$service_status['incumbency']){
                continue;
            }
            $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message', $this->lang));
            $msg_data = [
                "staff_users"       => [['id'=>$v]],
                "staff_info_ids_str"=> $v,
                "message_title"     => $t->t($titleKey,$titlePlaceholder), //标题
                "message_content"   => $t->t($contentKey,$contentPlaceholder),//内容
                "category"          => MessageEnums::CATEGORY_GENERAL,
            ];
            $bi_rpc->setParams($msg_data);
            $return = $bi_rpc->execute();//响应
            if (isset($return['result'])) {
                $this->logger->write_log("消息发送成功：". json_encode(['data'=>$msg_data,'result'=>$return]),'info');
            } else {
                $this->logger->write_log("消息发送失败：". json_encode(['data'=>$msg_data,'result'=>$return]),'notice');
            }
        }
    }

    public function suspendedReinstatementSendMsg($staff): bool
    {
        return true;
    }

    /**
     * 发送push
     * @param $params
     * @return void
     */
    public function sendPush($params)
    {
        $staffs              = $params['staffs'];
        $title_key           = $params['title_key'];
        $title_placeholder   = $params['title_placeholder'];
        $content_key         = $params['content_key'];
        $content_placeholder = $params['content_placeholder'];
        $staffServer         = new StaffServer();
        foreach ($staffs as $v) {
            $t = $this->getTranslation($staffServer->getLanguage($v));
            $staffInfo = $staffServer->getStaffInfoById($v);
            if ($staffInfo['state'] != enums::$service_status['incumbency']) {
                continue;
            }

            $data = [];
            $data['staff_info_id']   = $v;
            $data['src']             = 'backyard';
            $data['message_title']   = $t->t($title_key, $title_placeholder);
            $data['message_content'] = $t->t($content_key, $content_placeholder);
            $data['message_scheme']  = '';

            $this->logger->write_log('sendPush params:' . json_encode($data), 'info');
            $ret = (new ApiClient('bi_rpc', '', 'push_to_staff'));
            $ret->setParams($data);
            $result = $ret->execute();
            $this->logger->write_log(['function' => 'sendPush', 'params' => $params, 'result' => $result], 'info');
        }
    }

    /**
     * 更新is_new
     * @param $staff_info_id
     * @return mixed
     */
    public function updateReinstatementRequestOld($staff_info_id, $ref_id) {
        return $this->getDI()->get('db')->updateAsDict(
            'reinstatement_request',
            ['is_new' => ReinstatementRequestModel::IS_NEW_NO],
            [
                "conditions" => 'staff_info_id = ? and ref_id = ?',
                'bind'       => [$staff_info_id, $ref_id],
            ]
        );
    }


    /**
     * 停职记录列表
     * @param $staff_info_id
     * @return array
     */
    public function getSuspensionLogList($staff_info_id): array
    {
        $list = SuspensionManageLogModel::find([
            'conditions' => "staff_info_id = :staff_id:",
            'bind'       => ['staff_id' => $staff_info_id,],
            'order'      => 'id desc',
        ])->toArray();

        $not_refunded_list = $this->getStaffNotRefundedLog($staff_info_id);

        $data = [];
        //停职日期 停职原因 停职金额 最后回款日期
        foreach ($list as $key => $value) {
            $stop_duties_date = date('Y-m-d', strtotime($value['stop_duties_date']));
            $amount           = '-';
            $business_date    = '-';
            if ($value['stop_duty_reason'] == HrStaffInfoModel::STOP_DUTY_REASON_NOT_REFUNDED) {
                if (isset($not_refunded_list[$stop_duties_date])) {
                    $amount        = $not_refunded_list[$stop_duties_date]['not_refunded'];
                    $business_date = $not_refunded_list[$stop_duties_date]['last_refund_date'] ?? '-';
                }
            }

            $data[] = [
                'staff_info_id'         => $value['staff_info_id'],
                'stop_duties_date'      => $stop_duties_date,
                'stop_duty_reason'      => $value['stop_duty_reason'],
                'stop_duty_reason_text' => $this->getTranslation()->t('stop_duty_reason_' . $value['stop_duty_reason']),
                'amount'                => $amount,
                'last_refund_date'      => $business_date,
            ];
        }

        return $data;
    }


    /**
     * 未回款记录
     * @param $staff_info_id
     * @return array
     */
    public function getStaffNotRefundedLog($staff_info_id): array
    {
        $list = HrStaffNotRefundedSuspensionTaskModel::find([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => ['staff_info_id' => $staff_info_id],
        ])->toArray();
        $data = [];
        foreach ($list as $value) {
            $content_arr  = json_decode($value['content_json'], true);
            $not_refunded = $value['business_type'] == HrStaffNotRefundedSuspensionTaskModel::BUSINESS_TYPE_COURIER_NOT_REFUNDED_COURIER ? $content_arr['not_pay'] : $content_arr['sum_amount'];

            $data[$value['business_date']] = [
                'staff_info_id'    => $value['staff_info_id'],
                'not_refunded'     => $not_refunded / 100,
                'last_refund_date' => !empty($value['refund_time']) ? date('Y-m-d', strtotime($value['refund_time'])) : '',
            ];
        }
        return $data;
    }

    //恢复在职操作 任务 和审批通过调用
    public function reinstatement(ReinstatementRequestModel $value, $date,$is_task=false){
        //任务过来的没有员工信息
        if($is_task){
            $staffServer = new StaffServer();
            $this->staffInfo =  $staffServer->getStaffInfoById($value->staff_info_id);
        }

        //如果 非停职状态 直接返回 不操作 只更新 执行状态
        if($this->staffInfo['state'] != enums::$service_status['suspension']){
            return false;
        }

        //停职记录
        $log = $this->getSuspensionLog($value->ref_id);
        //恢复在职
        $data = [
            'staff_info_id' => $this->staffInfo['staff_info_id'],
            'operator_id' => $this->staffInfo['staff_info_id'],
            'type'=>1,

        ];
        if (!empty($log->origin_info)){
            $origin_info = json_decode($log->origin_info, true);
            if ($origin_info['wait_leave_state'] == 1 && $origin_info['leave_date'] > $date){
                $data['wait_leave_state'] = 1;
                $data['state'] = enums::$service_status['incumbency'];
            }else{
                $data['state'] = enums::$service_status['incumbency'];
                $data['wait_leave_state'] = 0;
            }

        }else{
            $data['state'] = enums::$service_status['incumbency'];
        }
        $result1 = $this->updateState($data);
        //Hold 处理
        $holdData = $this->getHoldDataFormat($this->staffInfo['staff_info_id'], $value->reason);
        $result2 = $this->handleHold($holdData);

        return [$result1,$result2];

    }



    private function updateState($staffData)
    {
        $client =  new ApiClient('hr_rpc','','sync_staff_state');
        $client->setParams([$staffData]);
        $res = $client->execute();
        $this->getDI()->get('logger')->write_log('员工状态恢复：'. json_encode($staffData) . '; 结果：'. json_encode($res), 'info');
        return $res['result']['code'] ?? 0;
    }

    public function handleHold($params)
    {
        $client =  new ApiClient('hcm_rpc','','sync_release_hold');
        $client->setParams($params);
        $res = $client->execute();
        $this->getDI()->get('logger')->write_log('Hold处理：'. json_encode($params) . '; 结果：'. json_encode($res), 'info');
        return $res['result']['code'] ?? 0 ;
    }

    /**
     * handleHold参数
     * @param $staff_info_id
     * @param $reason
     * @return array
     */
    public function getHoldDataFormat($staff_info_id, $reason): array
    {
        switch ($reason){
            case HrStaffInfoModel::STOP_DUTY_REASON_ABSENTEEISM: //停职原因 旷工
                $holdSource = '3';//hold来源 旷工
                $holdReason = 'off_3_days';
                break;
            case HrStaffInfoModel::STOP_DUTY_REASON_NOT_REFUNDED: //停职原因 未回款
                $holdSource = '4'; //hold来源 未回款
                $holdReason = 'fail_to_submit_public_funds';
                break;
            default:
                $holdSource = '';
                $holdReason = '';
                break;
        }
        $holdData = [];
        $holdData['staff_info_id'] = $staff_info_id;
        $holdData['hold_source'] = $holdSource;
        $holdData['hold_reason'] = $holdReason;
        $holdData['handle_hold'] = 1;
        $holdData['operator_id'] = $staff_info_id;

        return $holdData;
    }



}