<?php
namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Repository\StaffRepository;

class ShareCenterServer extends BaseServer {
    
    /**
     * 权限判断类别
     *
     */
    const SHARE_CENTER_FILE_PERMISSION_DEPARTMENT = 1;
    const SHARE_CENTER_FILE_PERMISSION_DEPARTMENT_ALL = -1;
    const SHARE_CENTER_FILE_PERMISSION_JOBTITLE = 2;
    const SHARE_CENTER_FILE_PERMISSION_JOBTITLE_ALL = -2;
    const SHARE_CENTER_FILE_PERMISSION_ROLE = 3;
    const SHARE_CENTER_FILE_PERMISSION_ROLE_ALL = -3;
    const SHARE_CENTER_FILE_PERMISSION_JOB_TITLE_GRADE = 4;
    const SHARE_CENTER_FILE_PERMISSION_JOB_TITLE_GRADE_ALL = -4;
    const SHARE_CENTER_FILE_PERMISSION_STAFFIDS = 5;

    /**
     *
     * 权限分类解释
     */
    const SHARE_CENTER_FILE_PERMISSION_DESC = [
        self::SHARE_CENTER_FILE_PERMISSION_DEPARTMENT => '按部门',
        self::SHARE_CENTER_FILE_PERMISSION_DEPARTMENT_ALL => '所有部门',
        self::SHARE_CENTER_FILE_PERMISSION_JOBTITLE => '按职位',
        self::SHARE_CENTER_FILE_PERMISSION_JOBTITLE_ALL => '所有职位',
        self::SHARE_CENTER_FILE_PERMISSION_ROLE => '按角色',
        self::SHARE_CENTER_FILE_PERMISSION_ROLE_ALL => '所有角色',
        self::SHARE_CENTER_FILE_PERMISSION_JOB_TITLE_GRADE => '按职等职级',
        self::SHARE_CENTER_FILE_PERMISSION_JOB_TITLE_GRADE_ALL => '所有职等职级',
        self::SHARE_CENTER_FILE_PERMISSION_STAFFIDS => '按工号'
    ];

    /**
     *
     *
     *  需要判断权限
     */
    const SHARE_CENTER_FILE_PERMISSION_YES = [
        self::SHARE_CENTER_FILE_PERMISSION_DEPARTMENT,
        self::SHARE_CENTER_FILE_PERMISSION_JOBTITLE,
        self::SHARE_CENTER_FILE_PERMISSION_ROLE,
        self::SHARE_CENTER_FILE_PERMISSION_JOB_TITLE_GRADE,
        self::SHARE_CENTER_FILE_PERMISSION_STAFFIDS

    ];

    public $timezone;
    public function __construct($lang = 'zh-CN', $timezone)
    {
        $this->timezone = $timezone;
        parent::__construct($lang);
    }

    /**
     * @var array
     * 基于判断文件权限的用户信息
     */
    private $staffsInfoForFilePermission = [];


    /**
     * 获取共享信息中心列表文件
     * @param $staffId
     * @param int $id
     * @return array
     */
    public function getShareCenterList($staffId, int $id = 0)
    {
        if (!empty($id)) {
            $info = $this->get_dir_info($id);
        }
        if (empty($info)) {
            //顶级
            $list = $this->getAllFilesTreeByStaff($staffId);
        } else {
            $list = $this->getAllFilesTreeByStaff($staffId, $info['level'], $info['level_code']);
        }

        // 本期只在泰国加
        //if (isCountry('TH')) {
        $haveNewFile = false;
        $this->setNewFilePath($list, [], $haveNewFile);
        //}

        if ($id) {
            $list = $this->getItemTreeById($list, $id);
        }
        return $list;
    }

    /**
     * Notes: 获取有新文件的文件夹
     * @param array $list 处理好的树形文件、文件夹
     * @param array $upDir 文件的上级文件夹目录
     * @param bool $haveNewFile 所有的文件中是否有新文件
     */
    private function setNewFilePath(array &$list, array $upDir, bool &$haveNewFile)
    {
        foreach ($list as &$value) {
            $value['is_new'] = false;
            if (!empty($value['file'])) {
                // 检查是否存在未超过7天的文件
                foreach ($value['file'] as &$f) {
                    $f['is_new'] = false;
                    // 使用show_time_zone将数据库中的0时区转为实际国家的时区在进行比较
                    $dateInt = strtotime(date('Y-m-d',
                        strtotime('+7 days', strtotime(show_time_zone($f['created_at'])))));
                    if (time() < $dateInt) {
                        $haveNewFile     = true;
                        $f['is_new']     = true;
                        $value['is_new'] = true;
                        // 回溯上级，将文件夹都设置有新文件
                        foreach ($upDir as &$item) {
                            $item = true;
                        }
                    }
                }
            }
            if (!empty($value['dir'])) {
                $tempUpDir = $upDir;
                // 将文件夹的引用记录下来，找到新文件之后循环该数组回溯上级文件夹
                $tempUpDir[] = &$value['is_new'];
                $this->setNewFilePath($value['dir'], $tempUpDir, $haveNewFile);
            }
        }
    }

    /**
     * Notes: 检查文件共享中心是否有新文件
     * @param $staffId
     * @return bool
     */
    public function checkShareCenterHaveNewFile($staffId): bool
    {
        $list = $this->getAllFilesTreeByStaff($staffId);
        $haveNewFile = false;
        $this->setNewFilePath($list, [], $haveNewFile);
        return $haveNewFile;
    }



    /**
     *
     * list 来源于方法 getAllFilesTreeByStaff
     *
     * 根据id过滤选择一项
     * @param $list
     * @param $id
     * @return array
     *
     */
    private function getItemTreeById($list, $id)
    {
        foreach ($list as $item) {
            if ($item['id'] == $id) {
                return [$item];
            } else if ($item['dir']) {
                return $this->getItemTreeById($item['dir'], $id);
            }
        }
        return [];
    }

    private function file_tree($staffId, $level = 1, $levelCodePrefix = ''){

    }

    /**
     * 获取全部文件及目录 递归 选择
     *
     *
     * @param $staffId
     * @param int $level
     * @param string $levelCodePrefix
     * @param $file_name
     * @return array
     *
     */
    private function getAllFilesTreeByStaff($staffId, $level = 1, $levelCodePrefix = '')
    {
        $sql = "
        --
        select
            scd.id,
            scd.name_en,
            scd.name_th,
            scd.name_cn,
            scd.icon,
            scd.level_code,
            scd.level,
            scd.is_default,
            scf.id f_id,
            scf.name f_name,
            scf.file_url,
            scf.view_type,
            scf.is_delete,
            scf.created_at
        from
            share_center_dir scd
        left join
            share_center_file scf
        on scd.level_code = scf.dir_level_code
        where
            scd.is_delete = 0 and scd.level = " . $level;

        //条件 有scf 导致 left join 没用 无法取出 该层级目录子级目录下的文件 scf.is_delete = 0 去掉

        if ($levelCodePrefix) {
            $sql .= " and scd.level_code like '" . $levelCodePrefix . "%' ";
        }

        $sql .= " order by scd.is_default DESC, scd.id ASC";

        $fileList = $this->getDI()->get('db_oa')->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $treeList = [];
        if ($fileList) {
            //获取对应语言环境的 名称
            $column_name = $this->get_lang_column($this->lang);
            foreach ($fileList as $item) {
                if (!isset($treeList[$item['id']])) {
                    // 文件夹
                    $treeList[$item['id']] = [
                        "id" => $item['id'],
                        "name" => $item[$column_name] ?? '',
                        "icon" => $item['icon'],
                        "dir" =>  $this->getAllFilesTreeByStaff($staffId, $item['level'] + 1, $item['level_code']),
                        "file" => []
                    ];
                }
                if ($item['is_delete'] == 1) {
                    continue;
                }

                if (
                    in_array($item['view_type'], self::SHARE_CENTER_FILE_PERMISSION_YES)
                    && $this->isHasFilePermission($staffId, $item['f_id'])
                    || !in_array($item['view_type'], self::SHARE_CENTER_FILE_PERMISSION_YES)
                ) {
                    // 文件
                    if(!empty($item['f_id'])){
                        $treeList[$item['id']]['file'][] = [
                            'id' => $item['f_id'],
                            'name' => $item['f_name'],
                            'file_url' => $item['file_url'],
                            'created_at' => $item['created_at'],
                        ];
                    }
                }
            }

            foreach ($treeList as $id =>  $dir) {
                if (!$dir['dir'] && !$dir['file']) {
                    unset($treeList[$id]);
                }
            }
        }

        return array_values($treeList);
    }

    /**
     *
     * 判断文件访问权限
     * @param $staffId
     * @param $fileId
     * @return bool
     */
    private function isHasFilePermission($staffId, $fileId)
    {
        $sql = "
        --
        select * from share_center_file where id = " . $fileId;
        $file = $this->getDI()->get("db_oa")->query($sql)->fetch(\PDO::FETCH_ASSOC);
        if ($file && $file['view_type'] == -1){
            return true;
        }

        if (!isset($this->staffsInfoForFilePermission[$staffId])) {
            $sql = "
            --
            select
                sys_department_id, -- 部门ID
                node_department_id,
                job_title, -- 职位ID
                group_concat(hsip.position_category) position_categories , -- 职位IDs
                job_title_grade
            from 
                hr_staff_info hsi
            left join
                hr_staff_info_position hsip
            on
                hsi.staff_info_id = hsip.staff_info_id
            where
                hsi.staff_info_id = " . $staffId;

            $info = $this->getDI()->get("db_rby")->query($sql)->fetch(\PDO::FETCH_ASSOC);
            if (!$info) {
                return false;
            }

            $this->staffsInfoForFilePermission[$staffId] = $info;
        }

        $staffInfo = $this->staffsInfoForFilePermission[$staffId];
        $sql = "
        --
        select
            scf.view_type,
            group_concat(scp.permission_value) permission_values
        from
            share_center_file scf
        left join
            share_center_permission scp
        on
            scf.id = scp.file_id
        where
            scf.is_delete = 0 and scp.is_delete = 0 and scf.id = {$fileId} group by scf.id";

        $permision = $this->getDI()->get("db_oa")->query($sql)->fetch(\PDO::FETCH_ASSOC);
        if (!($permision && isset($permision['view_type']) && isset($permision['permission_values']))) {
            return false;
        }
        if ($permision['view_type'] == self::SHARE_CENTER_FILE_PERMISSION_DEPARTMENT
            && in_array($staffInfo['node_department_id'], explode(",", $permision['permission_values']))
            ||
            $permision['view_type'] == self::SHARE_CENTER_FILE_PERMISSION_JOBTITLE
            && in_array($staffInfo['job_title'], explode(",", $permision['permission_values']))
            ||
            $permision['view_type'] == self::SHARE_CENTER_FILE_PERMISSION_ROLE
            && array_intersect(explode(",", $staffInfo['position_categories']) , explode(",", $permision['permission_values']))
            ||
            $permision['view_type'] == self::SHARE_CENTER_FILE_PERMISSION_JOB_TITLE_GRADE
            && in_array($staffInfo['job_title_grade'], explode(",", $permision['permission_values']))
            ||
            $permision['view_type'] == self::SHARE_CENTER_FILE_PERMISSION_STAFFIDS
            && in_array($staffId, explode(",", $permision['permission_values']))
        ) {

            return true;
        }

        return false;

    }


    //根据id 获取 目录的信息
    public function get_dir_info($id){
        $sql = "select * from share_center_dir where id = :id";
        return $this->getDI()->get("db_oa")->query($sql,['id'=>$id])->fetch(\PDO::FETCH_ASSOC);
    }



    //搜索所有文件 模糊查询
    public function search_all_file($param){

        if(empty($param['file_name']))
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        $dir_info = array();
        if(!empty($param['id']))
            $dir_info = $this->get_dir_info($param['id']);

        if(empty($dir_info) && !empty($param['id']))
            return $this->checkReturn(-3, $this->getTranslation()->_('wrong id'));
        $param['code'] = empty($dir_info) ? '' : $dir_info['level_code'];

//        $department_id = $param['staff_info']['department_id'];//这有可能是子部门 重新查信息
        $model = new StaffRepository($this->lang);
        $staff_info = $model -> getStaffPosition($param['staff_info']['id']);
        $department_id = $staff_info['sys_department_id'];
        if (empty($staff_info['sys_department_id']) || empty($staff_info['node_department_id'])) {
            throw new ValidationException('wrong department');
        }

        $sql = "select f.* ,group_concat(p.permission_value) as permission_value 
                ,date_format(CONVERT_TZ(f.created_at,  '+00:00', '{$this->timezone}'),'%Y-%m-%d %H:%i:%s') created_time 
                ,date_format(CONVERT_TZ(f.updated_at,  '+00:00', '{$this->timezone}'),'%Y-%m-%d %H:%i:%s') updated_time
                from share_center_file f
                left join share_center_permission p on f.id = p.file_id
                ";

        $where = " where f.is_delete = 0 
                    and (f.view_type = -1 or p.permission_value = {$staff_info['node_department_id']} or f.add_department_id = {$department_id})
                    ";

        if(!empty($param['code']))
            $where .= "and f.dir_level_code like '{$param['code']}%'";
        //搜索条件
        if(!empty($param['file_name']))
            $where .= " and f.`name` like '%{$param['file_name']}%' ";

        $where .= " group by f.id  ";

        $data = $this->getDI()->get('db_oa')->fetchAll($sql . $where);

        $return = array();
        if(!empty($data)){
            foreach($data as $da){
                $row['id'] = $da['id'];
                $row['name'] = $da['name'];
                $row['file_url'] = $da['file_url'];
                $return[] = $row;
            }
        }

        $res['data'] = $return;
        return $this->checkReturn($res);

    }








}