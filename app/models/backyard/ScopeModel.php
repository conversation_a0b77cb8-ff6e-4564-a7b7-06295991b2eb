<?php
namespace FlashExpress\bi\App\Models\backyard;

use League\OAuth2\Server\Entities\ScopeEntityInterface;

/**
 * <AUTHOR> <<EMAIL>>
 * Class ScopeModel
 * @property int id
 * @property string scope
 * @property int is_default
 * @package App\Models
 */
class ScopeModel extends BackyardBaseModel implements ScopeEntityInterface
{
    public function initialize()
    {
        parent::initialize();
        $this->setSource('oauth_scopes');
    }

    /**
     * @return string
     */
    public function getIdentifier()
    {
        return $this->scope;
    }

    /**
     * @param $identifier
     * @return $this
     */
    public function setIdentifier($identifier)
    {
        $this->scope = $identifier;
        return $this;
    }

    public function jsonSerialize()
    {
        // TODO: Implement jsonSerialize() method.
    }
}
