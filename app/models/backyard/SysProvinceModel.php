<?php

namespace FlashExpress\bi\App\Models\backyard;

class SysProvinceModel extends BackyardBaseModel
{
    protected $table_name = 'sys_province';

    /**
     *
     * Created by: Lqz.
     * @param array $provincesCode
     * @return array
     * CreateTime: 2020/8/14 0014 16:50
     */
    public static function getProvincesArrByCodeArr(array $provincesCode, $field = '*')
    {
        $provincesObj = SysProvinceModel::find([
            'conditions' => 'code in({provinceCodeArr:array})',
            'bind'       => ['provinceCodeArr' => $provincesCode],
            'columns'    => $field
        ]);
        $provincesArr = $provincesObj->toArray();
        if ($provincesArr) {
            $provincesArr = array_column($provincesArr, null, 'code');
        }
        return $provincesArr;
    }
}