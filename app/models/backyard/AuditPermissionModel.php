<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/2/25
 * Time: 7:59 PM
 */


namespace FlashExpress\bi\App\Models\backyard;

class AuditPermissionModel  extends BackyardBaseModel
{

    const MODULE_P_1 = 1;//加班申请页面 类型选项权限值
    const MODULE_P_2 = 2;//资产 申请权限控制
    const MODULE_P_3 = 3;//耗材 申请权限控制
    protected $table_name = 'audit_permission';


    public static function get_permission($module_id,$version = ''){
        $condition = "module_type = {$module_id}";
        if(!empty($version))
            $condition .= " and version = '{$version}'";
        return self::find($condition)->toArray();
    }
}