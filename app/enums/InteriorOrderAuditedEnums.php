<?php
/**
 *InteriorOrderAuditedEnums.php
 * Created by: Lqz.
 * Description:
 * User: Administrator
 * CreateTime: 2020/8/9 0009 23:11
 */

namespace FlashExpress\bi\App\Enums;

use FlashExpress\bi\App\Enums\BaseEnums;

class InteriorOrderAuditedEnums extends BaseEnums
{
    // 审核状态 0未审核，1审核通过，2审核不通过
    const IS_AUDITED_WARITING_CODE = 0; //0未审核
    const IS_AUDITED_SUCCESS_CODE  = 1;  //1审核通过
    const IS_AUDITED_FAILED_CODE   = 2;   //2审核不通过


    public static function getCodeTxtMap($lang = '')
    {
        $data = [
            self::IS_AUDITED_WARITING_CODE => '未审核',
            self::IS_AUDITED_SUCCESS_CODE  => '审核通过',
            self::IS_AUDITED_FAILED_CODE   => '审核不通过',
        ];
        return $data;
    }
}