<?php
// 车辆信息管理 2.0版本 2021.03

namespace FlashExpress\bi\App\Enums;

class VehicleInfoEnums extends BaseEnums
{
    // 翻译系统前缀
    const TRANSLATION_PREFIX = 'vehicle_info_';

    // 车辆来源
    const TRANSLATION_PREFIX_VEHICLE_SOURCE = self::TRANSLATION_PREFIX.'vehicle_source_';
    const VEHICLE_SOURCE_PERSONAL_CODE = 1;// 使用个人车辆
    const VEHICLE_SOURCE_RENTAL_CODE = 2;// 租用公司车辆
    const VEHICLE_SOURCE_BORROW_CODE = 3;// 借用车辆
    const VEHICLE_SOURCE_ITEM = [
        self::VEHICLE_SOURCE_PERSONAL_CODE => self::TRANSLATION_PREFIX_VEHICLE_SOURCE.self::VEHICLE_SOURCE_PERSONAL_CODE,
        self::VEHICLE_SOURCE_RENTAL_CODE => self::TRANSLATION_PREFIX_VEHICLE_SOURCE.self::VEHICLE_SOURCE_RENTAL_CODE,
        self::VEHICLE_SOURCE_BORROW_CODE => self::TRANSLATION_PREFIX_VEHICLE_SOURCE.self::VEHICLE_SOURCE_BORROW_CODE,
    ];

    // 驾照类型
    const TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE = self::TRANSLATION_PREFIX.'driver_license_type_';
    const DRIVER_LICENSE_TYPE_001 = 1;//临时私家驾照
    const DRIVER_LICENSE_TYPE_002 = 2;//临时私人摩托车驾照
    const DRIVER_LICENSE_TYPE_003 = 3;//私家车驾照
    const DRIVER_LICENSE_TYPE_004 = 4;//私家车驾照 第 1 类
    const DRIVER_LICENSE_TYPE_005 = 5;//私家车驾照 第 2 类
    const DRIVER_LICENSE_TYPE_006 = 6;//私家车驾照 第 3 类
    const DRIVER_LICENSE_TYPE_007 = 7;//私家车驾照 第 4 类
    const DRIVER_LICENSE_TYPE_008 = 8;//商用车驾照
    const DRIVER_LICENSE_TYPE_009 = 9;//商用车驾照 第 1 类
    const DRIVER_LICENSE_TYPE_010 = 10;//商用车驾照 第 2 类
    const DRIVER_LICENSE_TYPE_011 = 11;//商用车驾照 第 3 类
    const DRIVER_LICENSE_TYPE_012 = 12;//商用车驾照 第 4 类
    const DRIVER_LICENSE_TYPE_013 = 13;//私人摩托车驾照
    const DRIVER_LICENSE_TYPE_014 = 14;//商用摩托车驾照
    const DRIVER_LICENSE_TYPE_100 = 100;//其他 支持手动输入
    const DRIVER_LICENSE_TYPE_ITEM = [
        self::DRIVER_LICENSE_TYPE_001 => self::TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE.self::DRIVER_LICENSE_TYPE_001,
        self::DRIVER_LICENSE_TYPE_002 => self::TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE.self::DRIVER_LICENSE_TYPE_002,
        self::DRIVER_LICENSE_TYPE_003 => [
            self::DRIVER_LICENSE_TYPE_004 => self::TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE.self::DRIVER_LICENSE_TYPE_004,
            self::DRIVER_LICENSE_TYPE_005 => self::TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE.self::DRIVER_LICENSE_TYPE_005,
            self::DRIVER_LICENSE_TYPE_006 => self::TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE.self::DRIVER_LICENSE_TYPE_006,
            self::DRIVER_LICENSE_TYPE_007 => self::TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE.self::DRIVER_LICENSE_TYPE_007,
        ],
        self::DRIVER_LICENSE_TYPE_008 => [
            self::DRIVER_LICENSE_TYPE_009 => self::TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE.self::DRIVER_LICENSE_TYPE_009,
            self::DRIVER_LICENSE_TYPE_010 => self::TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE.self::DRIVER_LICENSE_TYPE_010,
            self::DRIVER_LICENSE_TYPE_011 => self::TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE.self::DRIVER_LICENSE_TYPE_011,
            self::DRIVER_LICENSE_TYPE_012 => self::TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE.self::DRIVER_LICENSE_TYPE_012,
        ],
        self::DRIVER_LICENSE_TYPE_013 => self::TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE.self::DRIVER_LICENSE_TYPE_013,
        self::DRIVER_LICENSE_TYPE_014 => self::TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE.self::DRIVER_LICENSE_TYPE_014,
        self::DRIVER_LICENSE_TYPE_100 => self::TRANSLATION_PREFIX_DRIVER_LICENSE_TYPE.self::DRIVER_LICENSE_TYPE_100,
    ];

    // 审核状态
    const TRANSLATION_PREFIX_APPROVAL_STATUS = self::TRANSLATION_PREFIX.'approval_status_';
    const APPROVAL_UN_SUBMITTED_CODE = 0;// 未提交
    const APPROVAL_PENDING_CODE = 1;//待审核
    const APPROVAL_PASSED_CODE = 2;//通过
    const APPROVAL_REJECT_CODE = 3;//驳回
    const APPROVAL_WAIT_NW_CODE = 6;//待nw审核
    const APPROVAL_STATUS_ITEM = [
        self::APPROVAL_UN_SUBMITTED_CODE => self::TRANSLATION_PREFIX_APPROVAL_STATUS.self::APPROVAL_UN_SUBMITTED_CODE,
        self::APPROVAL_PENDING_CODE => self::TRANSLATION_PREFIX_APPROVAL_STATUS.self::APPROVAL_PENDING_CODE,
        self::APPROVAL_PASSED_CODE => self::TRANSLATION_PREFIX_APPROVAL_STATUS.self::APPROVAL_PASSED_CODE,
        self::APPROVAL_REJECT_CODE => self::TRANSLATION_PREFIX_APPROVAL_STATUS.self::APPROVAL_REJECT_CODE,
    ];

    // 车辆添加渠道
    const VEHICLE_ADD_CHANNEL_KIT = 'kit';
    const VEHICLE_ADD_CHANNEL_FBI = 'fbi';

    // 车辆模块有权限的职位
    const JOB_VAN_TITLE_ID = 110;
    const JOB_VAN_PROJECT_TITLE_ID = 1015;
    const JOB_BIKE_TITLE_ID = 13;
    const JOB_TRICYCLE_TITLE_ID = 1000;
    const JOB_TRUCK_TITLE_ID = 1194;
    const JOB_VAN_FEEDER_TITLE_ID = 1497;
    const JOB_COURIER_AND_INSTALLATION_STAFF_TITLE_ID = 1663;
    const JOB_PICKUP_DRIVER = 1844;
    const JOB_EV_COURIER_TITLE_ID = 1930;

    const JOB_VAN_TITLE_NAME = 'Van Courier';
    const JOB_VAN_PROJECT_TITLE_NAME = 'Van Courier (Project)';
    const JOB_BIKE_TITLE_NAME = 'Bike Courier';
    const JOB_TRICYCLE_TITLE_NAME = 'Tricycle Courier';
    const JOB_TRUCK_TITLE_NAME = 'Truck Driver';
    const JOB_VAN_FEEDER_TITLE_NAME = 'Van Feeder';
    const JOB_COURIER_AND_INSTALLATION_STAFF_NAME = 'Courier and Installation Staff';
    const JOB_PICKUP_DRIVER_TITLE_NAME = 'Pickup Driver';
    const JOB_EV_COURIER_TITLE_NAME = 'EV Courier';

    const JOB_TITLE_ITEM = [
        self::JOB_VAN_TITLE_ID => self::JOB_VAN_TITLE_NAME,
        self::JOB_VAN_PROJECT_TITLE_ID => self::JOB_VAN_PROJECT_TITLE_NAME,
        self::JOB_BIKE_TITLE_ID => self::JOB_BIKE_TITLE_NAME,
        self::JOB_TRUCK_TITLE_ID => self::JOB_TRUCK_TITLE_NAME,
        self::JOB_TRICYCLE_TITLE_ID => self::JOB_TRICYCLE_TITLE_NAME,
        self::JOB_VAN_FEEDER_TITLE_ID => self::JOB_VAN_FEEDER_TITLE_NAME,
        self::JOB_COURIER_AND_INSTALLATION_STAFF_TITLE_ID => self::JOB_COURIER_AND_INSTALLATION_STAFF_NAME,
        self::JOB_PICKUP_DRIVER => self::JOB_PICKUP_DRIVER_TITLE_NAME,
        self::JOB_EV_COURIER_TITLE_ID => self::JOB_EV_COURIER_TITLE_NAME,

    ];


    // 车辆类型
    const VEHICLE_TYPE_BIKE_CODE = 0;
    const VEHICLE_TYPE_VAN_CODE = 1;
    const VEHICLE_TYPE_TRICYCLE_CODE = 2;
    const VEHICLE_TYPE_TRUCK_CODE = 4;
    const VEHICLE_TYPE_BIKE_TEXT = 'Bike';
    const VEHICLE_TYPE_VAN_TEXT = 'Van';
    const VEHICLE_TYPE_TRICYCLE_TEXT = 'Tricycle';
    const VEHICLE_TYPE_TRUCK_TEXT = 'Truck';

    const VEHICLE_TYPE_ITEM = [
        self::VEHICLE_TYPE_BIKE_CODE => self::VEHICLE_TYPE_BIKE_TEXT,
        self::VEHICLE_TYPE_VAN_CODE => self::VEHICLE_TYPE_VAN_TEXT,
        self::VEHICLE_TYPE_TRICYCLE_CODE => self::VEHICLE_TYPE_TRICYCLE_TEXT,
    ];



    // 职位与车辆类型
    const JOB_VEHICLE_TYPE_REL_CODE = [
        self::JOB_VAN_TITLE_ID => self::VEHICLE_TYPE_VAN_CODE,
        self::JOB_VAN_PROJECT_TITLE_ID => self::VEHICLE_TYPE_VAN_CODE,
        self::JOB_BIKE_TITLE_ID => self::VEHICLE_TYPE_BIKE_CODE,
        self::JOB_TRICYCLE_TITLE_ID => self::VEHICLE_TYPE_TRICYCLE_CODE,
        self::JOB_VAN_FEEDER_TITLE_ID => self::VEHICLE_TYPE_VAN_CODE,
        self::JOB_COURIER_AND_INSTALLATION_STAFF_TITLE_ID => self::VEHICLE_TYPE_VAN_CODE,
        self::JOB_PICKUP_DRIVER => self::VEHICLE_TYPE_VAN_CODE,
        self::JOB_EV_COURIER_TITLE_ID => self::VEHICLE_TYPE_VAN_CODE,
    ];

    // 职位与车辆类型
    const JOB_VEHICLE_TYPE_REL_TEXT = [
        self::JOB_VAN_TITLE_ID => self::VEHICLE_TYPE_VAN_TEXT,
        self::JOB_VAN_PROJECT_TITLE_ID => self::VEHICLE_TYPE_VAN_TEXT,
        self::JOB_BIKE_TITLE_ID => self::VEHICLE_TYPE_BIKE_TEXT,
        self::JOB_TRICYCLE_TITLE_ID => self::VEHICLE_TYPE_TRICYCLE_TEXT,
        self::JOB_VAN_FEEDER_TITLE_ID => self::VEHICLE_TYPE_VAN_TEXT,
        self::JOB_COURIER_AND_INSTALLATION_STAFF_TITLE_ID => self::VEHICLE_TYPE_VAN_TEXT,
        self::JOB_PICKUP_DRIVER => self::VEHICLE_TYPE_VAN_TEXT,
    ];

    // van 相关职位分组
    const VAN_JOB_GROUP_ITEM = [
        self::JOB_VAN_TITLE_ID,
        self::JOB_VAN_PROJECT_TITLE_ID,
        self::JOB_VAN_FEEDER_TITLE_ID,
        self::JOB_COURIER_AND_INSTALLATION_STAFF_TITLE_ID,
        self::JOB_PICKUP_DRIVER,
        self::JOB_EV_COURIER_TITLE_ID,
    ];


    // 可从 HR-IS 系统 同步过来的枚举字段列表
    const HR_IS_STAFF_CAR_NO_KEY = 'CAR_NO';// 车牌号
    const HR_IS_STAFF_CAR_TYPE_KEY = 'CAR_TYPE';// 车牌类型
    const HR_IS_STAFF_MANGER_KEY = 'MANGER';// 直线主管
    const HR_IS_STAFF_DRIVER_LICENSE_KEY = 'DRIVER_LICENSE';//驾驶证号
    const HR_IS_STAFF_ITEMS = [
        self::HR_IS_STAFF_CAR_NO_KEY,
        self::HR_IS_STAFF_CAR_TYPE_KEY,
        self::HR_IS_STAFF_MANGER_KEY,
        self::HR_IS_STAFF_DRIVER_LICENSE_KEY,
    ];

    // 油类型
    const OIL_TYPE_001 = 1;//汽油
    const OIL_TYPE_002 = 2;//柴油

    public static function getCodeTxtMap($lang = '', $code = '')
    {
    }

    // 油卡公司
    const OIL_COMPANY_SHELL_CODE = 1;
    const OIL_COMPANY_PTT_CODE = 2;
    const OIL_COMPANY_PT_CODE = 3;
    const OIL_COMPANY_ITEM = [
        self::OIL_COMPANY_SHELL_CODE => 'Shell',
        self::OIL_COMPANY_PTT_CODE => 'PTT',
        self::OIL_COMPANY_PT_CODE => 'PT',
    ];

}
