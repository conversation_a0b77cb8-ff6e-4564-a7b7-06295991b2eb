<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <!-- Meta, title, CSS, favicons, etc. -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}{% endblock %} - {{title}} - FlashExpress</title>
    <!-- Bootstrap -->
    <link href="{{ static_url('css/bootstrap.min.css?_v=')~assetsVersion }}" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="{{ static_url('css/font-awesome.min.css?_v=')~assetsVersion }}" rel="stylesheet">
    <!-- NProgress -->
<!--    <link href="{{ static_url('css/nprogress.css?_v=')~assetsVersion }}" rel="stylesheet">-->
    <!-- artDialog -->
    <link href="{{ static_url('css/dialog.css?_v=')~assetsVersion }}" rel="stylesheet">
    <link href="{{ static_url('css/bi.css?_v=')~assetsVersion }}" rel="stylesheet">

    {% block css %}{% endblock %}

    <!-- Custom Theme Style -->
    <link href="{{ static_url('css/custom.min.css?_v=')~assetsVersion }}" rel="stylesheet">
</head>

<body class="nav-md">
    <div class="container body">
    <div class="main_container">
        <div class="col-md-3 left_col">
            <div class="left_col scroll-view">
                <div class="navbar nav_title" style="border: 0;">
                    <a href="/" class="site_title"><span>Flash Express BI</span></a>
                    <!--<img src="/images/logo.png" alt="..." style="" class=" profile_img">-->
                </div>

                <div class="clearfix"></div>

                <!-- menu profile quick info -->
                {% block leftprofile %}{{ partial('public/leftprofile') }}{% endblock %}
                <!-- /menu profile quick info -->

                <br />

                <!-- sidebar menu -->
                {% block leftsidebar %}{{ partial('public/leftsidebar') }}{% endblock %}
                <!-- /sidebar menu -->

                <!-- menu footer buttons -->
                {% block leftfootbtn %}{{ partial('public/leftfootbtn') }}{% endblock %}
                <!-- /menu footer buttons -->
            </div>
        </div>

        <!-- top navigation -->
        {% block pagetop %}{{ partial('public/pagetop') }}{% endblock %}
        <!-- /top navigation -->

        <!-- page content -->
        <div class="right_col" role="main">
            {% block pagecontent %}{{content() }}{% endblock %}
        </div>
        <!-- /page content -->

        <!-- footer content -->
        {% block pagefooter %}{{ partial('public/pagefooter')}}{% endblock %}
        <!-- /footer content -->
        </div>
    </div>

    <input type="hidden" id="hidLatestTaskCreated_at" value="{{LatestTaskCreated_at}}" />
    <input type="hidden" id="change_rate" value="{{change_rate}}" />
    <input type="hidden" id="change_time_type" value="{{change_time_type}}" />
    <input type="hidden" id="hidDayOfMonth" value="{{DayOfMonth}}" />
    <input type="hidden" id="hid_language" value="{{l}}" />




    {% block alert %}
    {{ partial('public/alert') }}
    {% endblock %}

    <!-- jQuery -->
    <script src="{{ static_url('js/jquery.min.js?_v=')~assetsVersion }}"></script>
    <!-- Bootstrap -->
    <script src="{{ static_url('js/bootstrap.min.js?_v=')~assetsVersion }}"></script>
    <!-- FastClick -->
<!--    <script src="{{ static_url('js/fastclick.js?_v=')~assetsVersion }}"></script>-->
    <!-- NProgress -->
<!--    <script src="{{ static_url('js/nprogress.js?_v=')~assetsVersion }}"></script>-->
    <!-- artdialog -->
    <script src="{{ static_url('js/artdialog/dialog.js?_v=')~assetsVersion }}"></script>
    <script src="{{ static_url('js/artdialog/dialog-plus.js?_v=')~assetsVersion }}"></script>
    <!-- Custom Theme Scripts -->

<!--    <script src="{{ static_url('js/custom.js?_v=')~assetsVersion }}"></script>-->

    <script src="{{ static_url('js/lang/%s.js?_v=')|format(l)~assetsVersion }}"></script>


    <script src="{{ static_url('js/bi.js?_v=')~assetsVersion }}"></script>
    <!--<script src="{{ static_url('js/lang/lang.js?_v=')~assetsVersion }}"></script>-->

    <script type="text/javascript">
        $(function () {
            window.ThisWeek = <?php echo json_encode($ThisWeek);?>;
            window.PrevWeek = <?php echo json_encode($PrevWeek);?>;
            window.ThisMonth = <?php echo json_encode($ThisMonth);?>;
            window.PrevMonth = <?php echo json_encode($PrevMonth);?>;
            window.Prev2Month = <?php echo json_encode($Prev2Month);?>;
            window.Prev3Month = <?php echo json_encode($Prev3Month);?>;
            window.Prev4Month = <?php echo json_encode($Prev4Month);?>;
            window.Prev5Month = <?php echo json_encode($Prev5Month);?>;
            window.Yestoday = <?php echo json_encode($Yestoday);?>;
            //alert(lang.aaa);
        });
    </script>

    <script>
    $(function() {
        var ajaxBack = $.ajax
        var ajaxCount = 0
        var allAjaxDone = function() {} 
        $.ajax = function(setting) {
            var cur = setting.success
            setting.success = function(c){
                if(c.code == -499){
                    location.href='/loginuser/index'
                    return 
                }
                if($.isFunction(cur)){
                    cur(c)
                }
            }
            ajaxCount++
            var cb = setting.complete
            setting.complete = function() {
                if ($.isFunction(cb)) { 
                    cb.apply(setting.context, arguments) 
                }
                ajaxCount--
                if (ajaxCount == 0 && $.isFunction(allAjaxDone)) {
                    allAjaxDone()
                }
            }
            ajaxBack(setting)
        }
    })
    </script>
    <!-- 自定义模块，子模块可以重写 njz20180627-->
    {% block js %}
    <!-- ECharts -->
    <script src="{{ static_url('js/echarts.min.js?_v=')~assetsVersion }}"></script>
    <script src="{{ static_url('js/world.js?_v=')~assetsVersion }}"></script>
    {% endblock %}

</body>
</html>
